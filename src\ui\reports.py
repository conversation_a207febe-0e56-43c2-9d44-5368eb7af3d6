import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import os
import sys
import datetime
import calendar
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from models.report import Report

class ReportsWindow:
    def __init__(self, root, db_connection, current_user):
        """Initialize the reports window"""
        self.root = root
        self.conn = db_connection
        self.current_user = current_user
        
        # Initialize models
        self.report_model = Report(self.conn)
        
        # Configure the window
        self.root.title(f"POSMADORA - التقارير - {current_user[3]}")
        self.root.geometry("1200x700")
        
        # Set window icon if available
        try:
            self.root.iconbitmap("resources/images/icon.ico")
        except:
            pass
        
        # Create a style
        self.style = ttk.Style()
        self.style.configure('TLabel', font=('Arial', 12))
        self.style.configure('TButton', font=('Arial', 12))
        self.style.configure('Header.TLabel', font=('Arial', 18, 'bold'))
        self.style.configure('Title.TLabel', font=('Arial', 14, 'bold'))
        
        # Create main container
        self.main_container = ttk.Frame(self.root, padding=10)
        self.main_container.pack(fill=tk.BOTH, expand=True)
        
        # Create header
        self.header_label = ttk.Label(self.main_container, text="تقارير النظام", style='Header.TLabel')
        self.header_label.pack(pady=(0, 20))
        
        # Create notebook for report tabs
        self.notebook = ttk.Notebook(self.main_container)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Create tabs
        self.create_sales_tab()
        self.create_inventory_tab()
        self.create_customers_tab()
        self.create_staff_tab()
        
        # Create bottom buttons
        self.button_frame = ttk.Frame(self.main_container)
        self.button_frame.pack(fill=tk.X, pady=10)
        
        self.print_btn = ttk.Button(self.button_frame, text="طباعة التقرير", command=self.print_report)
        self.print_btn.pack(side=tk.LEFT, padx=5)
        
        self.export_btn = ttk.Button(self.button_frame, text="تصدير التقرير", command=self.export_report)
        self.export_btn.pack(side=tk.LEFT, padx=5)
        
        self.refresh_btn = ttk.Button(self.button_frame, text="تحديث البيانات", command=self.refresh_report)
        self.refresh_btn.pack(side=tk.LEFT, padx=5)
        
        self.close_btn = ttk.Button(self.button_frame, text="إغلاق", command=self.root.destroy)
        self.close_btn.pack(side=tk.RIGHT, padx=5)
        
        # Load initial reports
        self.load_reports()
    
    def create_sales_tab(self):
        """Create the sales reports tab"""
        self.sales_tab = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(self.sales_tab, text="تقارير المبيعات")
        
        # Create top frame for report options
        options_frame = ttk.LabelFrame(self.sales_tab, text="خيارات التقرير", padding=10)
        options_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Report type
        ttk.Label(options_frame, text="نوع التقرير:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.sales_report_type_var = tk.StringVar(value="daily")
        report_types = ttk.Combobox(options_frame, textvariable=self.sales_report_type_var, 
                                   values=["daily", "weekly", "monthly"], width=15)
        report_types.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        report_types.bind("<<ComboboxSelected>>", self.on_sales_report_type_change)
        
        # Date selection
        ttk.Label(options_frame, text="التاريخ:").grid(row=0, column=2, sticky=tk.W, padx=(20, 5), pady=5)
        
        # Daily date
        self.daily_date_frame = ttk.Frame(options_frame)
        self.daily_date_frame.grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        
        # Create date entry (simplified for this example)
        self.daily_date_var = tk.StringVar(value=datetime.datetime.now().strftime("%Y-%m-%d"))
        ttk.Entry(self.daily_date_frame, textvariable=self.daily_date_var, width=12).pack(side=tk.LEFT)
        
        # Weekly date
        self.weekly_date_frame = ttk.Frame(options_frame)
        
        ttk.Label(self.weekly_date_frame, text="السنة:").pack(side=tk.LEFT, padx=(0, 5))
        self.weekly_year_var = tk.StringVar(value=str(datetime.datetime.now().year))
        ttk.Entry(self.weekly_date_frame, textvariable=self.weekly_year_var, width=6).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Label(self.weekly_date_frame, text="الأسبوع:").pack(side=tk.LEFT, padx=(0, 5))
        self.weekly_week_var = tk.StringVar(value=str(datetime.datetime.now().isocalendar()[1]))
        ttk.Entry(self.weekly_date_frame, textvariable=self.weekly_week_var, width=4).pack(side=tk.LEFT)
        
        # Monthly date
        self.monthly_date_frame = ttk.Frame(options_frame)
        
        ttk.Label(self.monthly_date_frame, text="السنة:").pack(side=tk.LEFT, padx=(0, 5))
        self.monthly_year_var = tk.StringVar(value=str(datetime.datetime.now().year))
        ttk.Entry(self.monthly_date_frame, textvariable=self.monthly_year_var, width=6).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Label(self.monthly_date_frame, text="الشهر:").pack(side=tk.LEFT, padx=(0, 5))
        self.monthly_month_var = tk.StringVar(value=str(datetime.datetime.now().month))
        months = [str(i) for i in range(1, 13)]
        ttk.Combobox(self.monthly_date_frame, textvariable=self.monthly_month_var, values=months, width=4).pack(side=tk.LEFT)
        
        # Show only the appropriate date frame
        self.show_date_frame()
        
        # Generate report button
        self.generate_sales_btn = ttk.Button(options_frame, text="إنشاء التقرير", 
                                           command=self.generate_sales_report)
        self.generate_sales_btn.grid(row=0, column=4, padx=20, pady=5)
        
        # Create content frame for report
        self.sales_content_frame = ttk.Frame(self.sales_tab)
        self.sales_content_frame.pack(fill=tk.BOTH, expand=True, pady=10)
    
    def create_inventory_tab(self):
        """Create the inventory reports tab"""
        self.inventory_tab = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(self.inventory_tab, text="تقارير المخزون")
        
        # Create top frame for report options
        options_frame = ttk.LabelFrame(self.inventory_tab, text="خيارات التقرير", padding=10)
        options_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Report type
        ttk.Label(options_frame, text="نوع التقرير:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.inventory_report_type_var = tk.StringVar(value="stock_status")
        report_types = ttk.Combobox(options_frame, textvariable=self.inventory_report_type_var, 
                                   values=["stock_status", "low_stock", "stock_value", "category_breakdown"], width=20)
        report_types.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Category filter
        ttk.Label(options_frame, text="التصنيف:").grid(row=0, column=2, sticky=tk.W, padx=(20, 5), pady=5)
        self.inventory_category_var = tk.StringVar(value="الكل")
        # This would be populated with actual categories
        categories = ["الكل", "Beverages", "Bakery", "Dairy", "Produce", "Electronics"]
        ttk.Combobox(options_frame, textvariable=self.inventory_category_var, values=categories, width=15).grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        
        # Generate report button
        self.generate_inventory_btn = ttk.Button(options_frame, text="إنشاء التقرير", 
                                               command=self.generate_inventory_report)
        self.generate_inventory_btn.grid(row=0, column=4, padx=20, pady=5)
        
        # Create content frame for report
        self.inventory_content_frame = ttk.Frame(self.inventory_tab)
        self.inventory_content_frame.pack(fill=tk.BOTH, expand=True, pady=10)
    
    def create_customers_tab(self):
        """Create the customers reports tab"""
        self.customers_tab = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(self.customers_tab, text="تقارير العملاء")
        
        # Create top frame for report options
        options_frame = ttk.LabelFrame(self.customers_tab, text="خيارات التقرير", padding=10)
        options_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Report type
        ttk.Label(options_frame, text="نوع التقرير:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.customers_report_type_var = tk.StringVar(value="top_customers")
        report_types = ttk.Combobox(options_frame, textvariable=self.customers_report_type_var, 
                                   values=["top_customers", "frequent_customers", "inactive_customers"], width=20)
        report_types.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Limit
        ttk.Label(options_frame, text="عدد العملاء:").grid(row=0, column=2, sticky=tk.W, padx=(20, 5), pady=5)
        self.customers_limit_var = tk.StringVar(value="10")
        ttk.Entry(options_frame, textvariable=self.customers_limit_var, width=5).grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        
        # Generate report button
        self.generate_customers_btn = ttk.Button(options_frame, text="إنشاء التقرير", 
                                               command=self.generate_customers_report)
        self.generate_customers_btn.grid(row=0, column=4, padx=20, pady=5)
        
        # Create content frame for report
        self.customers_content_frame = ttk.Frame(self.customers_tab)
        self.customers_content_frame.pack(fill=tk.BOTH, expand=True, pady=10)
    
    def create_staff_tab(self):
        """Create the staff reports tab"""
        self.staff_tab = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(self.staff_tab, text="تقارير الموظفين")
        
        # Create top frame for report options
        options_frame = ttk.LabelFrame(self.staff_tab, text="خيارات التقرير", padding=10)
        options_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Date range
        ttk.Label(options_frame, text="من تاريخ:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.staff_start_date_var = tk.StringVar(value=(datetime.datetime.now() - datetime.timedelta(days=30)).strftime("%Y-%m-%d"))
        ttk.Entry(options_frame, textvariable=self.staff_start_date_var, width=12).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(options_frame, text="إلى تاريخ:").grid(row=0, column=2, sticky=tk.W, padx=(20, 5), pady=5)
        self.staff_end_date_var = tk.StringVar(value=datetime.datetime.now().strftime("%Y-%m-%d"))
        ttk.Entry(options_frame, textvariable=self.staff_end_date_var, width=12).grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        
        # Generate report button
        self.generate_staff_btn = ttk.Button(options_frame, text="إنشاء التقرير", 
                                           command=self.generate_staff_report)
        self.generate_staff_btn.grid(row=0, column=4, padx=20, pady=5)
        
        # Create content frame for report
        self.staff_content_frame = ttk.Frame(self.staff_tab)
        self.staff_content_frame.pack(fill=tk.BOTH, expand=True, pady=10)
    
    def on_sales_report_type_change(self, event=None):
        """Handle sales report type change"""
        self.show_date_frame()
    
    def show_date_frame(self):
        """Show the appropriate date frame based on report type"""
        # Hide all date frames
        for frame in [self.daily_date_frame, self.weekly_date_frame, self.monthly_date_frame]:
            frame.grid_forget()
        
        # Show the appropriate frame
        report_type = self.sales_report_type_var.get()
        if report_type == "daily":
            self.daily_date_frame.grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        elif report_type == "weekly":
            self.weekly_date_frame.grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        elif report_type == "monthly":
            self.monthly_date_frame.grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
    
    def load_reports(self):
        """Load initial reports"""
        self.generate_sales_report()
    
    def generate_sales_report(self):
        """Generate and display sales report"""
        # Clear previous report
        for widget in self.sales_content_frame.winfo_children():
            widget.destroy()
        
        # Get report type
        report_type = self.sales_report_type_var.get()
        
        try:
            if report_type == "daily":
                # Get date
                date_str = self.daily_date_var.get()
                
                # Get report data
                report_data = self.report_model.get_daily_summary(date_str)
                
                if not report_data or not report_data['summary'][0]:
                    ttk.Label(self.sales_content_frame, text="لا توجد بيانات للتاريخ المحدد").pack(pady=50)
                    return
                
                # Create report content
                self.create_daily_sales_report(report_data, date_str)
                
            elif report_type == "weekly":
                # Get year and week
                year = int(self.weekly_year_var.get())
                week = int(self.weekly_week_var.get())
                
                # Get report data
                report_data = self.report_model.get_weekly_summary(year, week)
                
                if not report_data or not report_data['weekly_total'][0]:
                    ttk.Label(self.sales_content_frame, text="لا توجد بيانات للأسبوع المحدد").pack(pady=50)
                    return
                
                # Create report content
                self.create_weekly_sales_report(report_data)
                
            elif report_type == "monthly":
                # Get year and month
                year = int(self.monthly_year_var.get())
                month = int(self.monthly_month_var.get())
                
                # Get report data
                report_data = self.report_model.get_monthly_summary(year, month)
                
                if not report_data or not report_data['monthly_total'][0]:
                    ttk.Label(self.sales_content_frame, text="لا توجد بيانات للشهر المحدد").pack(pady=50)
                    return
                
                # Create report content
                self.create_monthly_sales_report(report_data)
        
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء التقرير: {e}")
    
    def create_daily_sales_report(self, report_data, date_str):
        """Create daily sales report content"""
        # Create a canvas with scrollbar
        canvas = tk.Canvas(self.sales_content_frame)
        scrollbar = ttk.Scrollbar(self.sales_content_frame, orient=tk.VERTICAL, command=canvas.yview)
        canvas.configure(yscrollcommand=scrollbar.set)
        
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Create a frame inside the canvas for the report content
        report_frame = ttk.Frame(canvas)
        canvas.create_window((0, 0), window=report_frame, anchor=tk.NW)
        
        # Report header
        ttk.Label(report_frame, text=f"تقرير المبيعات اليومي - {date_str}", style='Header.TLabel').pack(pady=(0, 20))
        
        # Summary section
        summary_frame = ttk.LabelFrame(report_frame, text="ملخص المبيعات", padding=10)
        summary_frame.pack(fill=tk.X, pady=10)
        
        # Extract summary data
        sale_count = report_data['summary'][0] or 0
        total_sales = report_data['summary'][1] or 0
        total_discounts = report_data['summary'][2] or 0
        total_taxes = report_data['summary'][3] or 0
        
        # Create summary grid
        ttk.Label(summary_frame, text="عدد المبيعات:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(summary_frame, text=str(sale_count), font=('Arial', 12, 'bold')).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(summary_frame, text="إجمالي المبيعات:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(summary_frame, text=f"{total_sales:.2f}", font=('Arial', 12, 'bold')).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(summary_frame, text="إجمالي الخصومات:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(summary_frame, text=f"{total_discounts:.2f}").grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(summary_frame, text="إجمالي الضرائب:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(summary_frame, text=f"{total_taxes:.2f}").grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Payment methods section
        if report_data['payment_methods']:
            payment_frame = ttk.LabelFrame(report_frame, text="طرق الدفع", padding=10)
            payment_frame.pack(fill=tk.X, pady=10)
            
            for i, method in enumerate(report_data['payment_methods']):
                ttk.Label(payment_frame, text=f"{method[0]}:").grid(row=i, column=0, sticky=tk.W, padx=5, pady=2)
                ttk.Label(payment_frame, text=f"{method[1]} مبيعات - {method[2]:.2f}").grid(row=i, column=1, sticky=tk.W, padx=5, pady=2)
        
        # Create charts frame
        charts_frame = ttk.Frame(report_frame)
        charts_frame.pack(fill=tk.X, pady=10)
        
        # Hourly sales chart
        if report_data['hourly_sales']:
            hourly_frame = ttk.LabelFrame(charts_frame, text="المبيعات حسب الساعة", padding=10)
            hourly_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
            
            # Extract data
            hours = [f"{int(hour[0]):02d}:00" for hour in report_data['hourly_sales']]
            sales = [float(hour[2]) for hour in report_data['hourly_sales']]
            
            # Create figure and axis
            fig, ax = plt.subplots(figsize=(6, 4))
            ax.bar(hours, sales, color='skyblue')
            
            # Customize chart
            ax.set_xlabel('الساعة')
            ax.set_ylabel('المبيعات')
            ax.set_title('المبيعات حسب الساعة')
            plt.xticks(rotation=45)
            plt.tight_layout()
            
            # Embed chart in tkinter
            canvas = FigureCanvasTkAgg(fig, hourly_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Top products chart
        if report_data['top_products']:
            products_frame = ttk.LabelFrame(charts_frame, text="المنتجات الأكثر مبيعاً", padding=10)
            products_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
            
            # Extract data (limit to top 5)
            top_products = report_data['top_products'][:5] if len(report_data['top_products']) > 5 else report_data['top_products']
            product_names = [product[0] for product in top_products]
            quantities = [int(product[1]) for product in top_products]
            
            # Create figure and axis
            fig, ax = plt.subplots(figsize=(6, 4))
            ax.barh(product_names, quantities, color='lightgreen')
            
            # Customize chart
            ax.set_xlabel('الكمية المباعة')
            ax.set_title('المنتجات الأكثر مبيعاً')
            plt.tight_layout()
            
            # Embed chart in tkinter
            canvas = FigureCanvasTkAgg(fig, products_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Top categories section
        if report_data['top_categories']:
            categories_frame = ttk.LabelFrame(report_frame, text="التصنيفات الأكثر مبيعاً", padding=10)
            categories_frame.pack(fill=tk.X, pady=10)
            
            # Create treeview
            columns = ('category', 'quantity', 'amount')
            tree = ttk.Treeview(categories_frame, columns=columns, show='headings', height=5)
            
            # Define headings
            tree.heading('category', text='التصنيف')
            tree.heading('quantity', text='الكمية')
            tree.heading('amount', text='المبلغ')
            
            # Define columns
            tree.column('category', width=200)
            tree.column('quantity', width=100, anchor=tk.CENTER)
            tree.column('amount', width=100, anchor=tk.E)
            
            # Add data
            for category in report_data['top_categories']:
                tree.insert('', tk.END, values=(
                    category[0],  # category
                    category[1],  # quantity
                    f"{category[2]:.2f}"  # amount
                ))
            
            tree.pack(fill=tk.BOTH, expand=True)
        
        # Staff performance section
        if report_data['staff_performance']:
            staff_frame = ttk.LabelFrame(report_frame, text="أداء الموظفين", padding=10)
            staff_frame.pack(fill=tk.X, pady=10)
            
            # Create treeview
            columns = ('name', 'sales', 'amount')
            tree = ttk.Treeview(staff_frame, columns=columns, show='headings', height=5)
            
            # Define headings
            tree.heading('name', text='الموظف')
            tree.heading('sales', text='عدد المبيعات')
            tree.heading('amount', text='المبلغ')
            
            # Define columns
            tree.column('name', width=200)
            tree.column('sales', width=100, anchor=tk.CENTER)
            tree.column('amount', width=100, anchor=tk.E)
            
            # Add data
            for staff in report_data['staff_performance']:
                tree.insert('', tk.END, values=(
                    staff[0],  # name
                    staff[1],  # sales count
                    f"{staff[2]:.2f}"  # amount
                ))
            
            tree.pack(fill=tk.BOTH, expand=True)
        
        # Update the scroll region
        report_frame.update_idletasks()
        canvas.configure(scrollregion=canvas.bbox("all"))
    
    def create_weekly_sales_report(self, report_data):
        """Create weekly sales report content"""
        # Create a canvas with scrollbar
        canvas = tk.Canvas(self.sales_content_frame)
        scrollbar = ttk.Scrollbar(self.sales_content_frame, orient=tk.VERTICAL, command=canvas.yview)
        canvas.configure(yscrollcommand=scrollbar.set)
        
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Create a frame inside the canvas for the report content
        report_frame = ttk.Frame(canvas)
        canvas.create_window((0, 0), window=report_frame, anchor=tk.NW)
        
        # Report header
        ttk.Label(report_frame, text=f"تقرير المبيعات الأسبوعي - {report_data['start_date']} إلى {report_data['end_date']}", 
                 style='Header.TLabel').pack(pady=(0, 20))
        
        # Summary section
        summary_frame = ttk.LabelFrame(report_frame, text="ملخص المبيعات", padding=10)
        summary_frame.pack(fill=tk.X, pady=10)
        
        # Extract summary data
        sale_count = report_data['weekly_total'][0] or 0
        total_sales = report_data['weekly_total'][1] or 0
        total_discounts = report_data['weekly_total'][2] or 0
        total_taxes = report_data['weekly_total'][3] or 0
        average_sale = report_data['weekly_total'][4] or 0
        
        # Create summary grid
        ttk.Label(summary_frame, text="عدد المبيعات:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(summary_frame, text=str(sale_count), font=('Arial', 12, 'bold')).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(summary_frame, text="إجمالي المبيعات:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(summary_frame, text=f"{total_sales:.2f}", font=('Arial', 12, 'bold')).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(summary_frame, text="إجمالي الخصومات:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(summary_frame, text=f"{total_discounts:.2f}").grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(summary_frame, text="إجمالي الضرائب:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(summary_frame, text=f"{total_taxes:.2f}").grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(summary_frame, text="متوسط قيمة المبيعات:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(summary_frame, text=f"{average_sale:.2f}").grid(row=4, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Daily breakdown chart
        if report_data['daily_sales']:
            daily_frame = ttk.LabelFrame(report_frame, text="المبيعات اليومية", padding=10)
            daily_frame.pack(fill=tk.X, pady=10)
            
            # Extract data
            days = [day[0] for day in report_data['daily_sales']]
            sales = [float(day[2]) for day in report_data['daily_sales']]
            
            # Create figure and axis
            fig, ax = plt.subplots(figsize=(10, 4))
            ax.bar(days, sales, color='skyblue')
            
            # Customize chart
            ax.set_xlabel('اليوم')
            ax.set_ylabel('المبيعات')
            ax.set_title('المبيعات اليومية')
            plt.xticks(rotation=45)
            plt.tight_layout()
            
            # Embed chart in tkinter
            canvas = FigureCanvasTkAgg(fig, daily_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Payment methods section
        if report_data['payment_methods']:
            payment_frame = ttk.LabelFrame(report_frame, text="طرق الدفع", padding=10)
            payment_frame.pack(fill=tk.X, pady=10)
            
            for i, method in enumerate(report_data['payment_methods']):
                ttk.Label(payment_frame, text=f"{method[0]}:").grid(row=i, column=0, sticky=tk.W, padx=5, pady=2)
                ttk.Label(payment_frame, text=f"{method[1]} مبيعات - {method[2]:.2f}").grid(row=i, column=1, sticky=tk.W, padx=5, pady=2)
        
        # Top products section
        if report_data['top_products']:
            products_frame = ttk.LabelFrame(report_frame, text="المنتجات الأكثر مبيعاً", padding=10)
            products_frame.pack(fill=tk.X, pady=10)
            
            # Create treeview
            columns = ('product', 'quantity', 'amount')
            tree = ttk.Treeview(products_frame, columns=columns, show='headings', height=5)
            
            # Define headings
            tree.heading('product', text='المنتج')
            tree.heading('quantity', text='الكمية')
            tree.heading('amount', text='المبلغ')
            
            # Define columns
            tree.column('product', width=200)
            tree.column('quantity', width=100, anchor=tk.CENTER)
            tree.column('amount', width=100, anchor=tk.E)
            
            # Add data
            for product in report_data['top_products']:
                tree.insert('', tk.END, values=(
                    product[0],  # product name
                    product[1],  # quantity
                    f"{product[2]:.2f}"  # amount
                ))
            
            tree.pack(fill=tk.BOTH, expand=True)
        
        # Staff performance section
        if report_data['staff_performance']:
            staff_frame = ttk.LabelFrame(report_frame, text="أداء الموظفين", padding=10)
            staff_frame.pack(fill=tk.X, pady=10)
            
            # Create treeview
            columns = ('name', 'sales', 'amount')
            tree = ttk.Treeview(staff_frame, columns=columns, show='headings', height=5)
            
            # Define headings
            tree.heading('name', text='الموظف')
            tree.heading('sales', text='عدد المبيعات')
            tree.heading('amount', text='المبلغ')
            
            # Define columns
            tree.column('name', width=200)
            tree.column('sales', width=100, anchor=tk.CENTER)
            tree.column('amount', width=100, anchor=tk.E)
            
            # Add data
            for staff in report_data['staff_performance']:
                tree.insert('', tk.END, values=(
                    staff[0],  # name
                    staff[1],  # sales count
                    f"{staff[2]:.2f}"  # amount
                ))
            
            tree.pack(fill=tk.BOTH, expand=True)
        
        # Update the scroll region
        report_frame.update_idletasks()
        canvas.configure(scrollregion=canvas.bbox("all"))
    
    def create_monthly_sales_report(self, report_data):
        """Create monthly sales report content"""
        # Create a canvas with scrollbar
        canvas = tk.Canvas(self.sales_content_frame)
        scrollbar = ttk.Scrollbar(self.sales_content_frame, orient=tk.VERTICAL, command=canvas.yview)
        canvas.configure(yscrollcommand=scrollbar.set)
        
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Create a frame inside the canvas for the report content
        report_frame = ttk.Frame(canvas)
        canvas.create_window((0, 0), window=report_frame, anchor=tk.NW)
        
        # Report header
        ttk.Label(report_frame, text=f"تقرير المبيعات الشهري - {report_data['month_name']} {report_data['year']}", 
                 style='Header.TLabel').pack(pady=(0, 20))
        
        # Summary section
        summary_frame = ttk.LabelFrame(report_frame, text="ملخص المبيعات", padding=10)
        summary_frame.pack(fill=tk.X, pady=10)
        
        # Extract summary data
        sale_count = report_data['monthly_total'][0] or 0
        total_sales = report_data['monthly_total'][1] or 0
        total_discounts = report_data['monthly_total'][2] or 0
        total_taxes = report_data['monthly_total'][3] or 0
        average_sale = report_data['monthly_total'][4] or 0
        
        # Create summary grid
        ttk.Label(summary_frame, text="عدد المبيعات:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(summary_frame, text=str(sale_count), font=('Arial', 12, 'bold')).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(summary_frame, text="إجمالي المبيعات:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(summary_frame, text=f"{total_sales:.2f}", font=('Arial', 12, 'bold')).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(summary_frame, text="إجمالي الخصومات:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(summary_frame, text=f"{total_discounts:.2f}").grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(summary_frame, text="إجمالي الضرائب:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(summary_frame, text=f"{total_taxes:.2f}").grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(summary_frame, text="متوسط قيمة المبيعات:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(summary_frame, text=f"{average_sale:.2f}").grid(row=4, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Create charts frame
        charts_frame = ttk.Frame(report_frame)
        charts_frame.pack(fill=tk.X, pady=10)
        
        # Daily sales chart
        if report_data['daily_sales']:
            daily_frame = ttk.LabelFrame(charts_frame, text="المبيعات اليومية", padding=10)
            daily_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
            
            # Extract data
            days = [day[0].split('-')[2] for day in report_data['daily_sales']]  # Extract day part
            sales = [float(day[2]) for day in report_data['daily_sales']]
            
            # Create figure and axis
            fig, ax = plt.subplots(figsize=(6, 4))
            ax.bar(days, sales, color='skyblue')
            
            # Customize chart
            ax.set_xlabel('اليوم')
            ax.set_ylabel('المبيعات')
            ax.set_title('المبيعات اليومية')
            plt.xticks(rotation=45)
            plt.tight_layout()
            
            # Embed chart in tkinter
            canvas = FigureCanvasTkAgg(fig, daily_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Weekly sales chart
        if report_data['weekly_sales']:
            weekly_frame = ttk.LabelFrame(charts_frame, text="المبيعات الأسبوعية", padding=10)
            weekly_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
            
            # Extract data
            weeks = [f"أسبوع {int(week[0]) + 1}" for week in report_data['weekly_sales']]
            sales = [float(week[2]) for week in report_data['weekly_sales']]
            
            # Create figure and axis
            fig, ax = plt.subplots(figsize=(6, 4))
            ax.bar(weeks, sales, color='lightgreen')
            
            # Customize chart
            ax.set_xlabel('الأسبوع')
            ax.set_ylabel('المبيعات')
            ax.set_title('المبيعات الأسبوعية')
            plt.tight_layout()
            
            # Embed chart in tkinter
            canvas = FigureCanvasTkAgg(fig, weekly_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Payment methods section
        if report_data['payment_methods']:
            payment_frame = ttk.LabelFrame(report_frame, text="طرق الدفع", padding=10)
            payment_frame.pack(fill=tk.X, pady=10)
            
            for i, method in enumerate(report_data['payment_methods']):
                ttk.Label(payment_frame, text=f"{method[0]}:").grid(row=i, column=0, sticky=tk.W, padx=5, pady=2)
                ttk.Label(payment_frame, text=f"{method[1]} مبيعات - {method[2]:.2f}").grid(row=i, column=1, sticky=tk.W, padx=5, pady=2)
        
        # Top products section
        if report_data['top_products']:
            products_frame = ttk.LabelFrame(report_frame, text="المنتجات الأكثر مبيعاً", padding=10)
            products_frame.pack(fill=tk.X, pady=10)
            
            # Create treeview
            columns = ('product', 'quantity', 'amount')
            tree = ttk.Treeview(products_frame, columns=columns, show='headings', height=5)
            
            # Define headings
            tree.heading('product', text='المنتج')
            tree.heading('quantity', text='الكمية')
            tree.heading('amount', text='المبلغ')
            
            # Define columns
            tree.column('product', width=200)
            tree.column('quantity', width=100, anchor=tk.CENTER)
            tree.column('amount', width=100, anchor=tk.E)
            
            # Add data
            for product in report_data['top_products']:
                tree.insert('', tk.END, values=(
                    product[0],  # product name
                    product[1],  # quantity
                    f"{product[2]:.2f}"  # amount
                ))
            
            tree.pack(fill=tk.BOTH, expand=True)
        
        # Top categories section
        if report_data['top_categories']:
            categories_frame = ttk.LabelFrame(report_frame, text="التصنيفات الأكثر مبيعاً", padding=10)
            categories_frame.pack(fill=tk.X, pady=10)
            
            # Create treeview
            columns = ('category', 'quantity', 'amount')
            tree = ttk.Treeview(categories_frame, columns=columns, show='headings', height=5)
            
            # Define headings
            tree.heading('category', text='التصنيف')
            tree.heading('quantity', text='الكمية')
            tree.heading('amount', text='المبلغ')
            
            # Define columns
            tree.column('category', width=200)
            tree.column('quantity', width=100, anchor=tk.CENTER)
            tree.column('amount', width=100, anchor=tk.E)
            
            # Add data
            for category in report_data['top_categories']:
                tree.insert('', tk.END, values=(
                    category[0],  # category
                    category[1],  # quantity
                    f"{category[2]:.2f}"  # amount
                ))
            
            tree.pack(fill=tk.BOTH, expand=True)
        
        # Staff performance section
        if report_data['staff_performance']:
            staff_frame = ttk.LabelFrame(report_frame, text="أداء الموظفين", padding=10)
            staff_frame.pack(fill=tk.X, pady=10)
            
            # Create treeview
            columns = ('name', 'sales', 'amount')
            tree = ttk.Treeview(staff_frame, columns=columns, show='headings', height=5)
            
            # Define headings
            tree.heading('name', text='الموظف')
            tree.heading('sales', text='عدد المبيعات')
            tree.heading('amount', text='المبلغ')
            
            # Define columns
            tree.column('name', width=200)
            tree.column('sales', width=100, anchor=tk.CENTER)
            tree.column('amount', width=100, anchor=tk.E)
            
            # Add data
            for staff in report_data['staff_performance']:
                tree.insert('', tk.END, values=(
                    staff[0],  # name
                    staff[1],  # sales count
                    f"{staff[2]:.2f}"  # amount
                ))
            
            tree.pack(fill=tk.BOTH, expand=True)
        
        # Update the scroll region
        report_frame.update_idletasks()
        canvas.configure(scrollregion=canvas.bbox("all"))
    
    def generate_inventory_report(self):
        """Generate and display inventory report"""
        # This would implement inventory report generation
        messagebox.showinfo("تقرير المخزون", "سيتم إنشاء تقرير المخزون")
    
    def generate_customers_report(self):
        """Generate and display customers report"""
        # This would implement customers report generation
        messagebox.showinfo("تقرير العملاء", "سيتم إنشاء تقرير العملاء")
    
    def generate_staff_report(self):
        """Generate and display staff report"""
        # This would implement staff report generation
        messagebox.showinfo("تقرير الموظفين", "سيتم إنشاء تقرير الموظفين")
    
    def print_report(self):
        """Print the current report"""
        # This would implement report printing
        messagebox.showinfo("طباعة التقرير", "سيتم طباعة التقرير الحالي")
    
    def export_report(self):
        """Export the current report to a file"""
        # This would implement report export
        file_path = filedialog.asksaveasfilename(
            title="تصدير التقرير",
            filetypes=[("Excel files", "*.xlsx"), ("PDF files", "*.pdf"), ("All files", "*.*")],
            defaultextension=".xlsx"
        )
        
        if file_path:
            messagebox.showinfo("تصدير التقرير", f"سيتم تصدير التقرير إلى {file_path}")
    
    def refresh_report(self):
        """Refresh the current report"""
        # Get current tab
        current_tab = self.notebook.index(self.notebook.select())
        
        if current_tab == 0:  # Sales tab
            self.generate_sales_report()
        elif current_tab == 1:  # Inventory tab
            self.generate_inventory_report()
        elif current_tab == 2:  # Customers tab
            self.generate_customers_report()
        elif current_tab == 3:  # Staff tab
            self.generate_staff_report()


# For testing the reports window independently
if __name__ == "__main__":
    import sqlite3
    from database.db_setup import DatabaseSetup
    
    # Setup database
    db = DatabaseSetup()
    db.connect()
    db.setup_tables()
    db.insert_sample_data()
    
    # Create root window
    root = tk.Tk()
    
    # Mock user for testing
    mock_user = (1, 'admin', 'admin123', 'System Administrator', 'admin', 'day', None, None, None, None, 1)
    
    # Create reports window
    reports_window = ReportsWindow(root, db.conn, mock_user)
    
    # Start the main loop
    root.mainloop()
    
    # Close database connection
    db.close()