import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import os
import sys
import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from PIL import Image, ImageTk
import calendar
import time

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from models.report import Report
from models.product import Product
from models.sale import Sale
from models.customer import Customer
from ui.styles import AppStyles, RoundedButton, GradientFrame, AnimatedProgressbar, ToastNotification, load_image, center_window

class Dashboard:
    def __init__(self, root, db_connection, current_user):
        """Initialize the dashboard window"""
        self.root = root
        self.conn = db_connection
        self.current_user = current_user
        
        # Initialize models
        self.report_model = Report(self.conn)
        self.product_model = Product(self.conn)
        self.sale_model = Sale(self.conn)
        self.customer_model = Customer(self.conn)
        
        # Configure the window
        self.root.title(f"POSMADORA - لوحة التحكم - {current_user[3]}")
        self.root.state('zoomed')  # Maximize window
        
        # Set window icon if available
        try:
            self.root.iconbitmap("resources/images/icon.ico")
        except:
            pass
        
        # Apply styles
        self.style = AppStyles.setup_styles(self.root)
        
        # Create main container
        self.main_container = tk.Frame(self.root, bg=AppStyles.BG_MAIN)
        self.main_container.pack(fill=tk.BOTH, expand=True)
        
        # Create sidebar
        self.sidebar_width = 240
        self.sidebar = tk.Frame(self.main_container, width=self.sidebar_width, bg=AppStyles.DARK)
        self.sidebar.pack(side=tk.RIGHT, fill=tk.Y)
        self.sidebar.pack_propagate(False)  # Prevent the sidebar from shrinking
        
        # Create content area
        self.content_area = tk.Frame(self.main_container, bg=AppStyles.BG_MAIN)
        self.content_area.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Setup sidebar
        self.setup_sidebar()
        
        # Setup content area
        self.setup_content_area()
        
        # Load dashboard data
        self.load_dashboard()
        
        # Start clock update
        self.update_datetime()
    
    def setup_sidebar(self):
        """Set up the sidebar with user info and navigation"""
        # User profile section
        profile_frame = tk.Frame(self.sidebar, bg=AppStyles.DARK, padx=15, pady=15)
        profile_frame.pack(fill=tk.X)
        
        # User avatar
        avatar_frame = tk.Frame(profile_frame, bg=AppStyles.DARK)
        avatar_frame.pack(pady=(0, 10))
        
        # Try to load user avatar or use placeholder
        try:
            avatar_path = "resources/images/avatar.png"
            avatar_image = load_image(avatar_path, 80, 80)
            if avatar_image:
                avatar_label = tk.Label(avatar_frame, image=avatar_image, bg=AppStyles.DARK)
                avatar_label.image = avatar_image  # Keep a reference
                avatar_label.pack()
            else:
                # Create a circular placeholder
                avatar_canvas = tk.Canvas(avatar_frame, width=80, height=80, bg=AppStyles.DARK, 
                                         highlightthickness=0)
                avatar_canvas.pack()
                
                # Draw circle with user initials
                avatar_canvas.create_oval(5, 5, 75, 75, fill=AppStyles.PRIMARY, outline="")
                
                # Add user initials
                initials = "".join([name[0].upper() for name in self.current_user[3].split() if name])
                if not initials:
                    initials = self.current_user[1][0].upper()
                
                avatar_canvas.create_text(40, 40, text=initials, 
                                         font=(AppStyles.FONT_FAMILY, 24, 'bold'),
                                         fill=AppStyles.TEXT_LIGHT)
        except Exception as e:
            print(f"Error loading avatar: {e}")
            # Create a circular placeholder
            avatar_canvas = tk.Canvas(avatar_frame, width=80, height=80, bg=AppStyles.DARK, 
                                     highlightthickness=0)
            avatar_canvas.pack()
            
            # Draw circle with user initials
            avatar_canvas.create_oval(5, 5, 75, 75, fill=AppStyles.PRIMARY, outline="")
            
            # Add user initials
            initials = "".join([name[0].upper() for name in self.current_user[3].split() if name])
            if not initials:
                initials = self.current_user[1][0].upper()
            
            avatar_canvas.create_text(40, 40, text=initials, 
                                     font=(AppStyles.FONT_FAMILY, 24, 'bold'),
                                     fill=AppStyles.TEXT_LIGHT)
        
        # User name
        user_name = tk.Label(profile_frame, text=self.current_user[3],
                            font=(AppStyles.FONT_FAMILY_ARABIC, 14, 'bold'),
                            fg=AppStyles.TEXT_LIGHT, bg=AppStyles.DARK)
        user_name.pack()
        
        # User role
        user_role = tk.Label(profile_frame, text=self.current_user[4],
                            font=(AppStyles.FONT_FAMILY_ARABIC, 12),
                            fg=AppStyles.TEXT_MUTED, bg=AppStyles.DARK)
        user_role.pack()
        
        # Separator
        ttk.Separator(self.sidebar, orient=tk.HORIZONTAL).pack(fill=tk.X, padx=15, pady=15)
        
        # Navigation menu
        nav_frame = tk.Frame(self.sidebar, bg=AppStyles.DARK, padx=15)
        nav_frame.pack(fill=tk.BOTH, expand=True)
        
        # Menu items
        menu_items = [
            {"text": "لوحة التحكم", "icon": "🏠", "command": self.show_dashboard},
            {"text": "نقطة البيع", "icon": "🛒", "command": self.show_pos},
            {"text": "المنتجات", "icon": "📦", "command": self.show_products},
            {"text": "العملاء", "icon": "👥", "command": self.show_customers},
            {"text": "التقارير", "icon": "📊", "command": self.show_reports},
            {"text": "الإعدادات", "icon": "⚙️", "command": self.show_settings},
        ]
        
        # Create menu buttons
        self.menu_buttons = []
        for item in menu_items:
            button_frame = tk.Frame(nav_frame, bg=AppStyles.DARK)
            button_frame.pack(fill=tk.X, pady=5)
            
            # Highlight the dashboard button initially
            if item["text"] == "لوحة التحكم":
                button_frame.configure(bg=AppStyles.PRIMARY)
            
            button = tk.Label(button_frame, 
                             text=f"{item['icon']}  {item['text']}", 
                             font=(AppStyles.FONT_FAMILY_ARABIC, 12),
                             fg=AppStyles.TEXT_LIGHT, 
                             bg=AppStyles.PRIMARY if item["text"] == "لوحة التحكم" else AppStyles.DARK,
                             padx=10, pady=8,
                             anchor=tk.W)
            button.pack(fill=tk.X)
            
            # Bind click event
            button.bind("<Button-1>", lambda e, cmd=item["command"]: cmd())
            button_frame.bind("<Button-1>", lambda e, cmd=item["command"]: cmd())
            
            # Bind hover events
            def on_enter(e, btn=button, frm=button_frame, is_active=item["text"] == "لوحة التحكم"):
                if not is_active:
                    frm.configure(bg=AppStyles.PRIMARY_DARK)
                    btn.configure(bg=AppStyles.PRIMARY_DARK)
            
            def on_leave(e, btn=button, frm=button_frame, is_active=item["text"] == "لوحة التحكم"):
                if not is_active:
                    frm.configure(bg=AppStyles.DARK)
                    btn.configure(bg=AppStyles.DARK)
            
            button.bind("<Enter>", on_enter)
            button.bind("<Leave>", on_leave)
            button_frame.bind("<Enter>", on_enter)
            button_frame.bind("<Leave>", on_leave)
            
            # Store button references
            self.menu_buttons.append((button_frame, button, item["text"]))
        
        # Logout button at the bottom
        logout_frame = tk.Frame(self.sidebar, bg=AppStyles.DARK, padx=15, pady=15)
        logout_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        logout_button = tk.Label(logout_frame, 
                                text="🚪  تسجيل الخروج", 
                                font=(AppStyles.FONT_FAMILY_ARABIC, 12),
                                fg=AppStyles.DANGER, 
                                bg=AppStyles.DARK,
                                padx=10, pady=8,
                                anchor=tk.W)
        logout_button.pack(fill=tk.X)
        
        # Bind click event
        logout_button.bind("<Button-1>", lambda e: self.logout())
        logout_frame.bind("<Button-1>", lambda e: self.logout())
        
        # Bind hover events
        def on_logout_enter(e):
            logout_frame.configure(bg=AppStyles.DANGER)
            logout_button.configure(bg=AppStyles.DANGER, fg=AppStyles.TEXT_LIGHT)
        
        def on_logout_leave(e):
            logout_frame.configure(bg=AppStyles.DARK)
            logout_button.configure(bg=AppStyles.DARK, fg=AppStyles.DANGER)
        
        logout_button.bind("<Enter>", on_logout_enter)
        logout_button.bind("<Leave>", on_logout_leave)
        logout_frame.bind("<Enter>", on_logout_enter)
        logout_frame.bind("<Leave>", on_logout_leave)
    
    def setup_content_area(self):
        """Set up the main content area"""
        # Header
        self.header = tk.Frame(self.content_area, bg=AppStyles.BG_MAIN, height=60)
        self.header.pack(fill=tk.X)
        self.header.pack_propagate(False)  # Prevent the header from shrinking
        
        # Page title
        self.page_title = tk.Label(self.header, text="لوحة التحكم",
                                  font=(AppStyles.FONT_FAMILY_ARABIC, 18, 'bold'),
                                  fg=AppStyles.TEXT_DARK, bg=AppStyles.BG_MAIN)
        self.page_title.pack(side=tk.RIGHT, padx=20)
        
        # Date and time
        self.datetime_frame = tk.Frame(self.header, bg=AppStyles.BG_MAIN)
        self.datetime_frame.pack(side=tk.LEFT, padx=20)
        
        self.date_var = tk.StringVar()
        self.time_var = tk.StringVar()
        
        date_label = tk.Label(self.datetime_frame, textvariable=self.date_var,
                             font=(AppStyles.FONT_FAMILY_ARABIC, 10),
                             fg=AppStyles.TEXT_MUTED, bg=AppStyles.BG_MAIN)
        date_label.pack(anchor=tk.W)
        
        time_label = tk.Label(self.datetime_frame, textvariable=self.time_var,
                             font=(AppStyles.FONT_FAMILY_ARABIC, 12, 'bold'),
                             fg=AppStyles.TEXT_DARK, bg=AppStyles.BG_MAIN)
        time_label.pack(anchor=tk.W)
        
        # Separator
        separator = ttk.Separator(self.content_area, orient=tk.HORIZONTAL)
        separator.pack(fill=tk.X)
        
        # Dashboard content
        self.dashboard_content = tk.Frame(self.content_area, bg=AppStyles.BG_MAIN)
        self.dashboard_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    def update_datetime(self):
        """Update the date and time display"""
        now = datetime.datetime.now()
        
        # Update date and time variables
        self.date_var.set(now.strftime("%A, %d %B %Y"))
        self.time_var.set(now.strftime("%H:%M:%S"))
        
        # Schedule the next update
        self.root.after(1000, self.update_datetime)
    
    def load_dashboard(self):
        """Load dashboard content"""
        # Clear existing content
        for widget in self.dashboard_content.winfo_children():
            widget.destroy()
        
        # Create a canvas with scrollbar for the dashboard content
        canvas = tk.Canvas(self.dashboard_content, bg=AppStyles.BG_MAIN, highlightthickness=0)
        scrollbar = ttk.Scrollbar(self.dashboard_content, orient=tk.VERTICAL, command=canvas.yview)
        
        # Configure the canvas
        canvas.configure(yscrollcommand=scrollbar.set)
        canvas.bind('<Configure>', lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        
        # Create a frame inside the canvas for the dashboard content
        dashboard_frame = tk.Frame(canvas, bg=AppStyles.BG_MAIN)
        
        # Add the dashboard frame to the canvas
        canvas.create_window((0, 0), window=dashboard_frame, anchor=tk.NW, width=canvas.winfo_width())
        
        # Pack the canvas and scrollbar
        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Create dashboard sections
        self.create_summary_cards(dashboard_frame)
        self.create_charts_section(dashboard_frame)
        self.create_recent_activity_section(dashboard_frame)
    
    def create_summary_cards(self, parent):
        """Create summary cards section"""
        # Get today's date
        today = datetime.datetime.now().strftime("%Y-%m-%d")
        
        # Get summary data
        daily_summary = self.report_model.get_daily_summary(today)
        
        # Extract data
        if daily_summary and daily_summary['summary']:
            sale_count = daily_summary['summary'][0] or 0
            total_sales = daily_summary['summary'][1] or 0
        else:
            sale_count = 0
            total_sales = 0
        
        # Get low stock count
        low_stock_count = self.product_model.get_low_stock_count()
        
        # Get customer count
        customer_count = self.customer_model.get_customer_count()
        
        # Create a frame for the cards
        cards_frame = tk.Frame(parent, bg=AppStyles.BG_MAIN)
        cards_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Configure grid
        cards_frame.columnconfigure(0, weight=1)
        cards_frame.columnconfigure(1, weight=1)
        cards_frame.columnconfigure(2, weight=1)
        cards_frame.columnconfigure(3, weight=1)
        
        # Create summary cards
        self.create_summary_card(cards_frame, 0, "المبيعات اليوم", f"{sale_count}", 
                                "🛒", AppStyles.PRIMARY)
        
        self.create_summary_card(cards_frame, 1, "إجمالي المبيعات", f"{total_sales:.2f}", 
                                "💰", AppStyles.SUCCESS)
        
        self.create_summary_card(cards_frame, 2, "منتجات منخفضة المخزون", f"{low_stock_count}", 
                                "📦", AppStyles.WARNING)
        
        self.create_summary_card(cards_frame, 3, "عدد العملاء", f"{customer_count}", 
                                "👥", AppStyles.ACCENT)
    
    def create_summary_card(self, parent, column, title, value, icon, color):
        """Create a single summary card"""
        # Create card frame
        card = tk.Frame(parent, bg=AppStyles.LIGHT, padx=15, pady=15, 
                       highlightbackground=color, highlightthickness=1)
        card.grid(row=0, column=column, padx=10, pady=10, sticky=tk.NSEW)
        
        # Add icon
        icon_label = tk.Label(card, text=icon, font=(AppStyles.FONT_FAMILY, 24),
                             fg=color, bg=AppStyles.LIGHT)
        icon_label.pack(anchor=tk.W)
        
        # Add title
        title_label = tk.Label(card, text=title, font=(AppStyles.FONT_FAMILY_ARABIC, 12),
                              fg=AppStyles.TEXT_MUTED, bg=AppStyles.LIGHT)
        title_label.pack(anchor=tk.W)
        
        # Add value
        value_label = tk.Label(card, text=value, font=(AppStyles.FONT_FAMILY, 24, 'bold'),
                              fg=AppStyles.TEXT_DARK, bg=AppStyles.LIGHT)
        value_label.pack(anchor=tk.W, pady=(5, 0))
        
        # Make the card clickable
        card.bind("<Enter>", lambda e: card.configure(bg=AppStyles.BG_HIGHLIGHT))
        card.bind("<Leave>", lambda e: card.configure(bg=AppStyles.LIGHT))
        
        # Bind click events to all elements
        for widget in [card, icon_label, title_label, value_label]:
            widget.bind("<Enter>", lambda e, w=widget: w.configure(cursor="hand2"))
            widget.bind("<Leave>", lambda e, w=widget: w.configure(cursor=""))
            
            if title == "المبيعات اليوم":
                widget.bind("<Button-1>", lambda e: self.show_reports())
            elif title == "إجمالي المبيعات":
                widget.bind("<Button-1>", lambda e: self.show_reports())
            elif title == "منتجات منخفضة المخزون":
                widget.bind("<Button-1>", lambda e: self.show_products())
            elif title == "عدد العملاء":
                widget.bind("<Button-1>", lambda e: self.show_customers())
    
    def create_charts_section(self, parent):
        """Create charts section"""
        # Create a frame for the charts
        charts_frame = tk.Frame(parent, bg=AppStyles.BG_MAIN)
        charts_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Configure grid
        charts_frame.columnconfigure(0, weight=1)
        charts_frame.columnconfigure(1, weight=1)
        
        # Create sales chart
        self.create_sales_chart(charts_frame)
        
        # Create top products chart
        self.create_top_products_chart(charts_frame)
    
    def create_sales_chart(self, parent):
        """Create sales chart"""
        # Create a frame for the chart
        chart_frame = tk.Frame(parent, bg=AppStyles.LIGHT, padx=15, pady=15)
        chart_frame.grid(row=0, column=0, padx=10, pady=10, sticky=tk.NSEW)
        
        # Add title
        title_label = tk.Label(chart_frame, text="المبيعات الأسبوعية",
                              font=(AppStyles.FONT_FAMILY_ARABIC, 14, 'bold'),
                              fg=AppStyles.TEXT_DARK, bg=AppStyles.LIGHT)
        title_label.pack(anchor=tk.W, pady=(0, 10))
        
        # Get weekly sales data
        now = datetime.datetime.now()
        year = now.year
        week = now.isocalendar()[1]
        
        weekly_data = self.report_model.get_weekly_summary(year, week)
        
        if weekly_data and weekly_data['daily_sales']:
            # Extract data
            days = []
            sales = []
            
            for day in weekly_data['daily_sales']:
                # Convert date to day name
                date_obj = datetime.datetime.strptime(day[0], "%Y-%m-%d")
                day_name = date_obj.strftime("%a")
                days.append(day_name)
                sales.append(float(day[2] or 0))
            
            # Create figure and axis
            fig, ax = plt.subplots(figsize=(8, 4))
            
            # Plot data
            bars = ax.bar(days, sales, color=AppStyles.PRIMARY)
            
            # Add data labels
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                       f"{height:.1f}", ha='center', va='bottom',
                       fontsize=8)
            
            # Customize chart
            ax.set_ylabel('المبيعات')
            ax.set_title('المبيعات اليومية للأسبوع الحالي')
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            ax.set_facecolor(AppStyles.LIGHT)
            fig.patch.set_facecolor(AppStyles.LIGHT)
            
            # Embed chart in tkinter
            canvas = FigureCanvasTkAgg(fig, chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        else:
            # Show a message if no data
            no_data_label = tk.Label(chart_frame, text="لا توجد بيانات للأسبوع الحالي",
                                    font=(AppStyles.FONT_FAMILY_ARABIC, 12),
                                    fg=AppStyles.TEXT_MUTED, bg=AppStyles.LIGHT)
            no_data_label.pack(fill=tk.BOTH, expand=True, pady=50)
    
    def create_top_products_chart(self, parent):
        """Create top products chart"""
        # Create a frame for the chart
        chart_frame = tk.Frame(parent, bg=AppStyles.LIGHT, padx=15, pady=15)
        chart_frame.grid(row=0, column=1, padx=10, pady=10, sticky=tk.NSEW)
        
        # Add title
        title_label = tk.Label(chart_frame, text="المنتجات الأكثر مبيعاً",
                              font=(AppStyles.FONT_FAMILY_ARABIC, 14, 'bold'),
                              fg=AppStyles.TEXT_DARK, bg=AppStyles.LIGHT)
        title_label.pack(anchor=tk.W, pady=(0, 10))
        
        # Get top products data
        now = datetime.datetime.now()
        year = now.year
        month = now.month
        
        monthly_data = self.report_model.get_monthly_summary(year, month)
        
        if monthly_data and monthly_data['top_products']:
            # Extract data (limit to top 5)
            top_products = monthly_data['top_products'][:5] if len(monthly_data['top_products']) > 5 else monthly_data['top_products']
            
            product_names = []
            quantities = []
            
            for product in top_products:
                product_names.append(product[0])
                quantities.append(int(product[1] or 0))
            
            # Create figure and axis
            fig, ax = plt.subplots(figsize=(8, 4))
            
            # Plot data
            bars = ax.barh(product_names, quantities, color=AppStyles.SECONDARY)
            
            # Add data labels
            for bar in bars:
                width = bar.get_width()
                ax.text(width + 0.1, bar.get_y() + bar.get_height()/2.,
                       f"{width}", ha='left', va='center',
                       fontsize=8)
            
            # Customize chart
            ax.set_xlabel('الكمية المباعة')
            ax.set_title('المنتجات الأكثر مبيعاً هذا الشهر')
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            ax.set_facecolor(AppStyles.LIGHT)
            fig.patch.set_facecolor(AppStyles.LIGHT)
            
            # Embed chart in tkinter
            canvas = FigureCanvasTkAgg(fig, chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        else:
            # Show a message if no data
            no_data_label = tk.Label(chart_frame, text="لا توجد بيانات للشهر الحالي",
                                    font=(AppStyles.FONT_FAMILY_ARABIC, 12),
                                    fg=AppStyles.TEXT_MUTED, bg=AppStyles.LIGHT)
            no_data_label.pack(fill=tk.BOTH, expand=True, pady=50)
    
    def create_recent_activity_section(self, parent):
        """Create recent activity section"""
        # Create a frame for the section
        activity_frame = tk.Frame(parent, bg=AppStyles.BG_MAIN)
        activity_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Configure grid
        activity_frame.columnconfigure(0, weight=1)
        activity_frame.columnconfigure(1, weight=1)
        
        # Create recent sales table
        self.create_recent_sales_table(activity_frame)
        
        # Create low stock products table
        self.create_low_stock_table(activity_frame)
    
    def create_recent_sales_table(self, parent):
        """Create recent sales table"""
        # Create a frame for the table
        table_frame = tk.Frame(parent, bg=AppStyles.LIGHT, padx=15, pady=15)
        table_frame.grid(row=0, column=0, padx=10, pady=10, sticky=tk.NSEW)
        
        # Add title
        title_label = tk.Label(table_frame, text="آخر المبيعات",
                              font=(AppStyles.FONT_FAMILY_ARABIC, 14, 'bold'),
                              fg=AppStyles.TEXT_DARK, bg=AppStyles.LIGHT)
        title_label.pack(anchor=tk.W, pady=(0, 10))
        
        # Get recent sales data
        recent_sales = self.sale_model.get_recent_sales(5)
        
        if recent_sales:
            # Create treeview
            columns = ('invoice', 'customer', 'amount', 'time')
            tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=5)
            
            # Define headings
            tree.heading('invoice', text='رقم الفاتورة')
            tree.heading('customer', text='العميل')
            tree.heading('amount', text='المبلغ')
            tree.heading('time', text='الوقت')
            
            # Define columns
            tree.column('invoice', width=120)
            tree.column('customer', width=150)
            tree.column('amount', width=100, anchor=tk.E)
            tree.column('time', width=100, anchor=tk.CENTER)
            
            # Add data
            for sale in recent_sales:
                # Format time
                sale_time = datetime.datetime.strptime(sale[8], "%Y-%m-%d %H:%M:%S").strftime("%H:%M:%S")
                
                tree.insert('', tk.END, values=(
                    sale[1],  # invoice_number
                    sale[12] if sale[12] else "عميل عابر",  # customer_name
                    f"{sale[4]:.2f}",  # total_amount
                    sale_time  # formatted time
                ))
            
            tree.pack(fill=tk.BOTH, expand=True)
            
            # Bind double-click to view sale details
            tree.bind("<Double-1>", lambda e: self.view_sale_details(tree.item(tree.selection()[0], 'values')[0]))
        else:
            # Show a message if no data
            no_data_label = tk.Label(table_frame, text="لا توجد مبيعات حديثة",
                                    font=(AppStyles.FONT_FAMILY_ARABIC, 12),
                                    fg=AppStyles.TEXT_MUTED, bg=AppStyles.LIGHT)
            no_data_label.pack(fill=tk.BOTH, expand=True, pady=50)
    
    def create_low_stock_table(self, parent):
        """Create low stock products table"""
        # Create a frame for the table
        table_frame = tk.Frame(parent, bg=AppStyles.LIGHT, padx=15, pady=15)
        table_frame.grid(row=0, column=1, padx=10, pady=10, sticky=tk.NSEW)
        
        # Add title
        title_label = tk.Label(table_frame, text="منتجات منخفضة المخزون",
                              font=(AppStyles.FONT_FAMILY_ARABIC, 14, 'bold'),
                              fg=AppStyles.TEXT_DARK, bg=AppStyles.LIGHT)
        title_label.pack(anchor=tk.W, pady=(0, 10))
        
        # Get low stock products
        low_stock_products = self.product_model.get_low_stock_products(5)
        
        if low_stock_products:
            # Create treeview
            columns = ('name', 'quantity', 'min_quantity', 'status')
            tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=5)
            
            # Define headings
            tree.heading('name', text='المنتج')
            tree.heading('quantity', text='الكمية الحالية')
            tree.heading('min_quantity', text='الحد الأدنى')
            tree.heading('status', text='الحالة')
            
            # Define columns
            tree.column('name', width=200)
            tree.column('quantity', width=100, anchor=tk.CENTER)
            tree.column('min_quantity', width=100, anchor=tk.CENTER)
            tree.column('status', width=100, anchor=tk.CENTER)
            
            # Configure tags for status colors
            tree.tag_configure('out_of_stock', background=AppStyles.DANGER)
            tree.tag_configure('low_stock', background=AppStyles.WARNING)
            
            # Add data
            for product in low_stock_products:
                # Determine status
                if product[7] <= 0:
                    status = "نفذت الكمية"
                    tag = 'out_of_stock'
                else:
                    status = "منخفض المخزون"
                    tag = 'low_stock'
                
                tree.insert('', tk.END, values=(
                    product[2],  # name
                    product[7],  # quantity
                    product[8],  # min_quantity
                    status
                ), tags=(tag,))
            
            tree.pack(fill=tk.BOTH, expand=True)
            
            # Bind double-click to view product details
            tree.bind("<Double-1>", lambda e: self.show_product_details(tree.item(tree.selection()[0], 'values')[0]))
        else:
            # Show a message if no data
            no_data_label = tk.Label(table_frame, text="لا توجد منتجات منخفضة المخزون",
                                    font=(AppStyles.FONT_FAMILY_ARABIC, 12),
                                    fg=AppStyles.TEXT_MUTED, bg=AppStyles.LIGHT)
            no_data_label.pack(fill=tk.BOTH, expand=True, pady=50)
    
    def view_sale_details(self, invoice_number):
        """View details of a sale"""
        # This would be implemented to show sale details
        messagebox.showinfo("تفاصيل المبيعات", f"عرض تفاصيل الفاتورة: {invoice_number}")
    
    def show_product_details(self, product_name):
        """View details of a product"""
        # This would be implemented to show product details
        messagebox.showinfo("تفاصيل المنتج", f"عرض تفاصيل المنتج: {product_name}")
    
    def set_active_menu(self, menu_name):
        """Set the active menu item"""
        for frame, button, name in self.menu_buttons:
            if name == menu_name:
                frame.configure(bg=AppStyles.PRIMARY)
                button.configure(bg=AppStyles.PRIMARY)
            else:
                frame.configure(bg=AppStyles.DARK)
                button.configure(bg=AppStyles.DARK)
    
    def show_dashboard(self):
        """Show the dashboard"""
        self.set_active_menu("لوحة التحكم")
        self.page_title.configure(text="لوحة التحكم")
        self.load_dashboard()
    
    def show_pos(self):
        """Show the POS screen"""
        self.set_active_menu("نقطة البيع")
        self.page_title.configure(text="نقطة البيع")
        
        # This would be implemented to show the POS screen
        # For now, just show a message
        messagebox.showinfo("نقطة البيع", "سيتم فتح شاشة نقطة البيع")
    
    def show_products(self):
        """Show the products screen"""
        self.set_active_menu("المنتجات")
        self.page_title.configure(text="إدارة المنتجات")
        
        # This would be implemented to show the products screen
        # For now, just show a message
        messagebox.showinfo("المنتجات", "سيتم فتح شاشة إدارة المنتجات")
    
    def show_customers(self):
        """Show the customers screen"""
        self.set_active_menu("العملاء")
        self.page_title.configure(text="إدارة العملاء")
        
        # This would be implemented to show the customers screen
        # For now, just show a message
        messagebox.showinfo("العملاء", "سيتم فتح شاشة إدارة العملاء")
    
    def show_reports(self):
        """Show the reports screen"""
        self.set_active_menu("التقارير")
        self.page_title.configure(text="التقارير")
        
        # This would be implemented to show the reports screen
        # For now, just show a message
        messagebox.showinfo("التقارير", "سيتم فتح شاشة التقارير")
    
    def show_settings(self):
        """Show the settings screen"""
        self.set_active_menu("الإعدادات")
        self.page_title.configure(text="الإعدادات")
        
        # This would be implemented to show the settings screen
        # For now, just show a message
        messagebox.showinfo("الإعدادات", "سيتم فتح شاشة الإعدادات")
    
    def logout(self):
        """Log out the current user"""
        if messagebox.askyesno("تسجيل الخروج", "هل أنت متأكد من رغبتك في تسجيل الخروج؟"):
            # This would be implemented to log out and show the login screen
            messagebox.showinfo("تسجيل الخروج", "تم تسجيل الخروج بنجاح")
            
            # Clear the root window
            for widget in self.root.winfo_children():
                widget.destroy()
            
            # Import the login window here to avoid circular imports
            from ui.login import LoginWindow
            
            # Show login window
            login_window = LoginWindow(self.root, self.conn, self.on_login_success)
    
    def on_login_success(self, user):
        """Handle successful login"""
        self.current_user = user
        self.__init__(self.root, self.conn, self.current_user)


# For testing the dashboard independently
if __name__ == "__main__":
    import sqlite3
    from database.db_setup import DatabaseSetup
    
    # Setup database
    db = DatabaseSetup()
    db.connect()
    db.setup_tables()
    db.insert_sample_data()
    
    # Create root window
    root = tk.Tk()
    
    # Mock user for testing
    mock_user = (1, 'admin', 'admin123', 'System Administrator', 'admin', 'day', None, None, None, None, 1)
    
    # Create dashboard
    dashboard = Dashboard(root, db.conn, mock_user)
    
    # Start the main loop
    root.mainloop()
    
    # Close database connection
    db.close()