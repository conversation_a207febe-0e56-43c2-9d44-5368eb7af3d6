import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import sqlite3
import os
import sys
import datetime

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from models.customer import Customer
from models.sale import Sale

class CustomersWindow:
    def __init__(self, root, db_connection, current_user):
        """Initialize the customers management window"""
        self.root = root
        self.conn = db_connection
        self.current_user = current_user
        
        # Initialize models
        self.customer_model = Customer(self.conn)
        self.sale_model = Sale(self.conn)
        
        # Configure the window
        self.root.title(f"POSMADORA - إدارة العملاء - {current_user[3]}")
        self.root.geometry("1000x700")
        
        # Set window icon if available
        try:
            self.root.iconbitmap("resources/images/icon.ico")
        except:
            pass
        
        # Create a style
        self.style = ttk.Style()
        self.style.configure('TLabel', font=('Arial', 12))
        self.style.configure('TButton', font=('Arial', 12))
        self.style.configure('Header.TLabel', font=('Arial', 18, 'bold'))
        self.style.configure('Title.TLabel', font=('Arial', 14, 'bold'))
        
        # Create main container
        self.main_container = ttk.Frame(self.root, padding=10)
        self.main_container.pack(fill=tk.BOTH, expand=True)
        
        # Create header
        self.header_label = ttk.Label(self.main_container, text="إدارة العملاء", style='Header.TLabel')
        self.header_label.pack(pady=(0, 20))
        
        # Create top frame for search
        self.top_frame = ttk.Frame(self.main_container)
        self.top_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Search bar
        ttk.Label(self.top_frame, text="بحث:").pack(side=tk.LEFT, padx=5)
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(self.top_frame, textvariable=self.search_var, width=30)
        self.search_entry.pack(side=tk.LEFT, padx=5)
        
        self.search_btn = ttk.Button(self.top_frame, text="بحث", command=self.search_customers)
        self.search_btn.pack(side=tk.LEFT, padx=5)
        
        # Create main content with customers list and details
        self.content_frame = ttk.Frame(self.main_container)
        self.content_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # Create customers list on the left
        self.customers_frame = ttk.LabelFrame(self.content_frame, text="قائمة العملاء")
        self.customers_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # Create customer details on the right
        self.details_frame = ttk.LabelFrame(self.content_frame, text="تفاصيل العميل")
        self.details_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0), pady=5)
        
        # Create customers treeview
        self.create_customers_treeview()
        
        # Create customer details form
        self.create_customer_details()
        
        # Create bottom buttons
        self.button_frame = ttk.Frame(self.main_container)
        self.button_frame.pack(fill=tk.X, pady=10)
        
        self.add_btn = ttk.Button(self.button_frame, text="إضافة عميل جديد", command=self.add_customer)
        self.add_btn.pack(side=tk.LEFT, padx=5)
        
        self.edit_btn = ttk.Button(self.button_frame, text="تعديل العميل", command=self.edit_customer)
        self.edit_btn.pack(side=tk.LEFT, padx=5)
        
        self.delete_btn = ttk.Button(self.button_frame, text="حذف العميل", command=self.delete_customer)
        self.delete_btn.pack(side=tk.LEFT, padx=5)
        
        self.refresh_btn = ttk.Button(self.button_frame, text="تحديث القائمة", command=self.refresh_customers)
        self.refresh_btn.pack(side=tk.LEFT, padx=5)
        
        self.close_btn = ttk.Button(self.button_frame, text="إغلاق", command=self.root.destroy)
        self.close_btn.pack(side=tk.RIGHT, padx=5)
        
        # Load customers
        self.load_customers()
    
    def create_customers_treeview(self):
        """Create the customers treeview"""
        # Create frame for treeview with scrollbar
        tree_frame = ttk.Frame(self.customers_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create treeview
        columns = ('id', 'name', 'phone', 'email', 'loyalty', 'last_purchase')
        self.customers_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=20)
        
        # Define headings
        self.customers_tree.heading('id', text='#')
        self.customers_tree.heading('name', text='اسم العميل')
        self.customers_tree.heading('phone', text='رقم الهاتف')
        self.customers_tree.heading('email', text='البريد الإلكتروني')
        self.customers_tree.heading('loyalty', text='نقاط الولاء')
        self.customers_tree.heading('last_purchase', text='آخر عملية شراء')
        
        # Define columns
        self.customers_tree.column('id', width=50, anchor=tk.CENTER)
        self.customers_tree.column('name', width=200)
        self.customers_tree.column('phone', width=120)
        self.customers_tree.column('email', width=180)
        self.customers_tree.column('loyalty', width=80, anchor=tk.CENTER)
        self.customers_tree.column('last_purchase', width=150)
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.customers_tree.yview)
        self.customers_tree.configure(yscroll=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.customers_tree.pack(fill=tk.BOTH, expand=True)
        
        # Bind selection event
        self.customers_tree.bind('<<TreeviewSelect>>', self.on_customer_select)
    
    def create_customer_details(self):
        """Create the customer details form"""
        # Create a notebook for tabs
        self.details_notebook = ttk.Notebook(self.details_frame)
        self.details_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create tabs
        self.info_tab = ttk.Frame(self.details_notebook, padding=10)
        self.sales_tab = ttk.Frame(self.details_notebook, padding=10)
        
        self.details_notebook.add(self.info_tab, text="معلومات العميل")
        self.details_notebook.add(self.sales_tab, text="سجل المشتريات")
        
        # Customer ID (hidden)
        self.customer_id_var = tk.StringVar()
        
        # Basic information
        basic_frame = ttk.LabelFrame(self.info_tab, text="معلومات أساسية", padding=10)
        basic_frame.pack(fill=tk.X, pady=5)
        
        # Name
        ttk.Label(basic_frame, text="اسم العميل:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.name_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.name_var, width=30).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Phone
        ttk.Label(basic_frame, text="رقم الهاتف:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.phone_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.phone_var, width=30).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Email
        ttk.Label(basic_frame, text="البريد الإلكتروني:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.email_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.email_var, width=30).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Address
        ttk.Label(basic_frame, text="العنوان:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.address_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.address_var, width=30).grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Loyalty information
        loyalty_frame = ttk.LabelFrame(self.info_tab, text="برنامج الولاء", padding=10)
        loyalty_frame.pack(fill=tk.X, pady=5)
        
        # Loyalty points
        ttk.Label(loyalty_frame, text="نقاط الولاء:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.loyalty_points_var = tk.StringVar()
        ttk.Entry(loyalty_frame, textvariable=self.loyalty_points_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Add points button
        self.add_points_btn = ttk.Button(loyalty_frame, text="إضافة نقاط", command=self.add_loyalty_points)
        self.add_points_btn.grid(row=0, column=2, padx=5, pady=5)
        
        # Redeem points button
        self.redeem_points_btn = ttk.Button(loyalty_frame, text="استبدال نقاط", command=self.redeem_loyalty_points)
        self.redeem_points_btn.grid(row=0, column=3, padx=5, pady=5)
        
        # Last purchase
        ttk.Label(loyalty_frame, text="آخر عملية شراء:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.last_purchase_var = tk.StringVar()
        ttk.Label(loyalty_frame, textvariable=self.last_purchase_var).grid(row=1, column=1, columnspan=3, sticky=tk.W, padx=5, pady=5)
        
        # Notes
        notes_frame = ttk.LabelFrame(self.info_tab, text="ملاحظات", padding=10)
        notes_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.notes_text = tk.Text(notes_frame, height=5, width=40)
        self.notes_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create sales history tab
        self.create_sales_history_tab()
        
        # Action buttons
        action_frame = ttk.Frame(self.info_tab)
        action_frame.pack(fill=tk.X, pady=10)
        
        self.save_btn = ttk.Button(action_frame, text="حفظ التغييرات", command=self.save_customer)
        self.save_btn.pack(side=tk.LEFT, padx=5)
        
        self.cancel_btn = ttk.Button(action_frame, text="إلغاء", command=self.cancel_edit)
        self.cancel_btn.pack(side=tk.LEFT, padx=5)
        
        # Initially disable the form
        self.set_form_state(tk.DISABLED)
    
    def create_sales_history_tab(self):
        """Create the sales history tab"""
        # Create frame for treeview with scrollbar
        sales_tree_frame = ttk.Frame(self.sales_tab)
        sales_tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create treeview
        columns = ('id', 'date', 'invoice', 'items', 'total', 'payment')
        self.sales_tree = ttk.Treeview(sales_tree_frame, columns=columns, show='headings', height=15)
        
        # Define headings
        self.sales_tree.heading('id', text='#')
        self.sales_tree.heading('date', text='التاريخ')
        self.sales_tree.heading('invoice', text='رقم الفاتورة')
        self.sales_tree.heading('items', text='عدد العناصر')
        self.sales_tree.heading('total', text='المبلغ')
        self.sales_tree.heading('payment', text='طريقة الدفع')
        
        # Define columns
        self.sales_tree.column('id', width=50, anchor=tk.CENTER)
        self.sales_tree.column('date', width=150)
        self.sales_tree.column('invoice', width=150)
        self.sales_tree.column('items', width=80, anchor=tk.CENTER)
        self.sales_tree.column('total', width=100, anchor=tk.E)
        self.sales_tree.column('payment', width=100)
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(sales_tree_frame, orient=tk.VERTICAL, command=self.sales_tree.yview)
        self.sales_tree.configure(yscroll=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.sales_tree.pack(fill=tk.BOTH, expand=True)
        
        # Bind double-click to view sale details
        self.sales_tree.bind("<Double-1>", self.view_sale_details)
        
        # Summary frame
        summary_frame = ttk.Frame(self.sales_tab)
        summary_frame.pack(fill=tk.X, pady=5)
        
        # Total purchases
        ttk.Label(summary_frame, text="إجمالي المشتريات:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.total_purchases_var = tk.StringVar(value="0.00")
        ttk.Label(summary_frame, textvariable=self.total_purchases_var, font=('Arial', 12, 'bold')).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Purchase count
        ttk.Label(summary_frame, text="عدد عمليات الشراء:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.purchase_count_var = tk.StringVar(value="0")
        ttk.Label(summary_frame, textvariable=self.purchase_count_var).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Average purchase
        ttk.Label(summary_frame, text="متوسط قيمة الشراء:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.average_purchase_var = tk.StringVar(value="0.00")
        ttk.Label(summary_frame, textvariable=self.average_purchase_var).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
    
    def load_customers(self, customers=None):
        """Load customers into the treeview"""
        # Clear existing customers
        for item in self.customers_tree.get_children():
            self.customers_tree.delete(item)
        
        # Get customers if not provided
        if customers is None:
            customers = self.customer_model.get_all_customers()
        
        # Add customers to treeview
        for customer in customers:
            # Format last purchase date
            last_purchase = customer[7] if customer[7] else "لم يتم الشراء بعد"
            
            # Add to treeview
            self.customers_tree.insert('', tk.END, values=(
                customer[0],  # id
                customer[1],  # name
                customer[2] if customer[2] else "",  # phone
                customer[3] if customer[3] else "",  # email
                customer[5],  # loyalty_points
                last_purchase  # last_purchase
            ))
    
    def on_customer_select(self, event):
        """Handle customer selection"""
        selection = self.customers_tree.selection()
        if not selection:
            return
        
        # Get selected customer ID
        customer_id = self.customers_tree.item(selection[0], 'values')[0]
        
        # Get customer details
        customer = self.customer_model.get_customer_by_id(customer_id)
        if not customer:
            return
        
        # Enable the form
        self.set_form_state(tk.NORMAL)
        
        # Fill the form with customer details
        self.customer_id_var.set(customer[0])
        self.name_var.set(customer[1])
        self.phone_var.set(customer[2] if customer[2] else "")
        self.email_var.set(customer[3] if customer[3] else "")
        self.address_var.set(customer[4] if customer[4] else "")
        self.loyalty_points_var.set(customer[5])
        self.last_purchase_var.set(customer[7] if customer[7] else "لم يتم الشراء بعد")
        
        # Clear notes
        self.notes_text.delete(1.0, tk.END)
        
        # Load sales history
        self.load_sales_history(customer[0])
    
    def load_sales_history(self, customer_id):
        """Load sales history for the customer"""
        # Clear existing sales
        for item in self.sales_tree.get_children():
            self.sales_tree.delete(item)
        
        # Get sales for the customer
        sales = self.sale_model.get_sales_by_customer(customer_id)
        
        # Reset summary variables
        total_amount = 0
        purchase_count = len(sales)
        
        # Add sales to treeview
        for sale in sales:
            # Get sale items
            items = self.sale_model.get_sale_items(sale[0])
            item_count = len(items)
            
            # Add to treeview
            self.sales_tree.insert('', tk.END, values=(
                sale[0],  # id
                sale[8],  # sale_date
                sale[1],  # invoice_number
                item_count,  # number of items
                f"{sale[4]:.2f}",  # total_amount
                sale[6]  # payment_method
            ))
            
            # Update total amount
            total_amount += sale[4]
        
        # Update summary
        self.total_purchases_var.set(f"{total_amount:.2f}")
        self.purchase_count_var.set(str(purchase_count))
        
        # Calculate average purchase
        average = total_amount / purchase_count if purchase_count > 0 else 0
        self.average_purchase_var.set(f"{average:.2f}")
    
    def view_sale_details(self, event):
        """View details of a selected sale"""
        selection = self.sales_tree.selection()
        if not selection:
            return
        
        # Get selected sale ID
        sale_id = self.sales_tree.item(selection[0], 'values')[0]
        
        # Get sale details
        sale = self.sale_model.get_sale_by_id(sale_id)
        if not sale:
            return
        
        # Get sale items
        items = self.sale_model.get_sale_items(sale_id)
        
        # Create a new window to display sale details
        details_window = tk.Toplevel(self.root)
        details_window.title(f"تفاصيل الفاتورة - {sale[1]}")
        details_window.geometry("600x500")
        
        # Sale header
        header_frame = ttk.Frame(details_window, padding=10)
        header_frame.pack(fill=tk.X)
        
        ttk.Label(header_frame, text=f"فاتورة رقم: {sale[1]}", style='Title.TLabel').pack(anchor=tk.W)
        ttk.Label(header_frame, text=f"التاريخ: {sale[8]}").pack(anchor=tk.W)
        ttk.Label(header_frame, text=f"العميل: {sale[12]}").pack(anchor=tk.W)
        ttk.Label(header_frame, text=f"الموظف: {sale[13]}").pack(anchor=tk.W)
        
        # Items list
        items_frame = ttk.LabelFrame(details_window, text="العناصر", padding=10)
        items_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # Create treeview for items
        columns = ('name', 'price', 'quantity', 'total')
        items_tree = ttk.Treeview(items_frame, columns=columns, show='headings', height=10)
        
        # Define headings
        items_tree.heading('name', text='المنتج')
        items_tree.heading('price', text='السعر')
        items_tree.heading('quantity', text='الكمية')
        items_tree.heading('total', text='المجموع')
        
        # Define columns
        items_tree.column('name', width=200)
        items_tree.column('price', width=100, anchor=tk.E)
        items_tree.column('quantity', width=100, anchor=tk.CENTER)
        items_tree.column('total', width=100, anchor=tk.E)
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(items_frame, orient=tk.VERTICAL, command=items_tree.yview)
        items_tree.configure(yscroll=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        items_tree.pack(fill=tk.BOTH, expand=True)
        
        # Add items to treeview
        for item in items:
            items_tree.insert('', tk.END, values=(
                item[7],  # product_name
                f"{item[3]:.2f}",  # unit_price
                item[2],  # quantity
                f"{item[5]:.2f}"  # total_price
            ))
        
        # Totals
        totals_frame = ttk.Frame(details_window, padding=10)
        totals_frame.pack(fill=tk.X)
        
        ttk.Label(totals_frame, text="المجموع الفرعي:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Label(totals_frame, text=f"{sale[4] - sale[6] + sale[5]:.2f}").grid(row=0, column=1, sticky=tk.E, padx=5, pady=2)
        
        ttk.Label(totals_frame, text="الخصم:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Label(totals_frame, text=f"{sale[5]:.2f}").grid(row=1, column=1, sticky=tk.E, padx=5, pady=2)
        
        ttk.Label(totals_frame, text="الضريبة:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Label(totals_frame, text=f"{sale[6]:.2f}").grid(row=2, column=1, sticky=tk.E, padx=5, pady=2)
        
        ttk.Label(totals_frame, text="المجموع:", font=('Arial', 12, 'bold')).grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(totals_frame, text=f"{sale[4]:.2f}", font=('Arial', 12, 'bold')).grid(row=3, column=1, sticky=tk.E, padx=5, pady=5)
        
        # Payment info
        payment_frame = ttk.Frame(details_window, padding=10)
        payment_frame.pack(fill=tk.X)
        
        ttk.Label(payment_frame, text=f"طريقة الدفع: {sale[6]}").pack(anchor=tk.W)
        ttk.Label(payment_frame, text=f"حالة الدفع: {sale[7]}").pack(anchor=tk.W)
        
        # Close button
        ttk.Button(details_window, text="إغلاق", command=details_window.destroy).pack(pady=10)
    
    def set_form_state(self, state):
        """Enable or disable the form fields"""
        for child in self.info_tab.winfo_children():
            if isinstance(child, ttk.LabelFrame):
                for grandchild in child.winfo_children():
                    if isinstance(grandchild, (ttk.Entry, ttk.Combobox)):
                        grandchild.configure(state=state)
        
        # Enable/disable text widget
        self.notes_text.configure(state=state)
        
        # Enable/disable buttons
        if state == tk.DISABLED:
            self.save_btn.configure(state=tk.DISABLED)
            self.cancel_btn.configure(state=tk.DISABLED)
            self.add_points_btn.configure(state=tk.DISABLED)
            self.redeem_points_btn.configure(state=tk.DISABLED)
        else:
            self.save_btn.configure(state=tk.NORMAL)
            self.cancel_btn.configure(state=tk.NORMAL)
            self.add_points_btn.configure(state=tk.NORMAL)
            self.redeem_points_btn.configure(state=tk.NORMAL)
    
    def clear_form(self):
        """Clear the form fields"""
        self.customer_id_var.set("")
        self.name_var.set("")
        self.phone_var.set("")
        self.email_var.set("")
        self.address_var.set("")
        self.loyalty_points_var.set("0")
        self.last_purchase_var.set("لم يتم الشراء بعد")
        self.notes_text.delete(1.0, tk.END)
        
        # Clear sales history
        for item in self.sales_tree.get_children():
            self.sales_tree.delete(item)
        
        # Reset summary
        self.total_purchases_var.set("0.00")
        self.purchase_count_var.set("0")
        self.average_purchase_var.set("0.00")
    
    def add_customer(self):
        """Add a new customer"""
        # Clear and enable the form
        self.clear_form()
        self.set_form_state(tk.NORMAL)
        
        # Set default values
        self.loyalty_points_var.set("0")
        
        # Clear selection in treeview
        self.customers_tree.selection_remove(self.customers_tree.selection())
        
        # Switch to info tab
        self.details_notebook.select(0)
    
    def edit_customer(self):
        """Edit the selected customer"""
        selection = self.customers_tree.selection()
        if not selection:
            messagebox.showinfo("تنبيه", "الرجاء اختيار عميل أولاً")
            return
        
        # Enable the form
        self.set_form_state(tk.NORMAL)
        
        # Switch to info tab
        self.details_notebook.select(0)
    
    def save_customer(self):
        """Save the customer (add new or update existing)"""
        # Validate form
        if not self.validate_form():
            return
        
        try:
            # Get form values
            customer_id = self.customer_id_var.get()
            name = self.name_var.get()
            phone = self.phone_var.get()
            email = self.email_var.get()
            address = self.address_var.get()
            loyalty_points = int(self.loyalty_points_var.get())
            
            if customer_id:  # Update existing customer
                success = self.customer_model.update_customer(
                    customer_id, name, phone, email, address
                )
                
                # Update loyalty points if changed
                current_points = int(self.customers_tree.item(self.customers_tree.selection()[0], 'values')[4])
                if loyalty_points != current_points:
                    self.customer_model.update_loyalty_points(
                        customer_id, loyalty_points - current_points
                    )
                
                if success:
                    messagebox.showinfo("نجاح", "تم تحديث العميل بنجاح")
                else:
                    messagebox.showerror("خطأ", "فشل تحديث العميل")
            else:  # Add new customer
                new_id = self.customer_model.add_customer(
                    name, phone, email, address, loyalty_points
                )
                
                if new_id:
                    messagebox.showinfo("نجاح", "تم إضافة العميل بنجاح")
                else:
                    messagebox.showerror("خطأ", "فشل إضافة العميل")
            
            # Refresh customers list
            self.refresh_customers()
            
            # Disable form
            self.set_form_state(tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ العميل: {e}")
    
    def validate_form(self):
        """Validate the form fields"""
        # Check required fields
        if not self.name_var.get():
            messagebox.showwarning("تحذير", "الرجاء إدخال اسم العميل")
            return False
        
        # Validate loyalty points
        try:
            if self.loyalty_points_var.get():
                points = int(self.loyalty_points_var.get())
                if points < 0:
                    messagebox.showwarning("تحذير", "يجب أن تكون نقاط الولاء قيمة موجبة")
                    return False
        except ValueError:
            messagebox.showwarning("تحذير", "الرجاء إدخال قيمة صحيحة لنقاط الولاء")
            return False
        
        return True
    
    def cancel_edit(self):
        """Cancel editing and disable the form"""
        self.set_form_state(tk.DISABLED)
        
        # If a customer is selected, reload its details
        selection = self.customers_tree.selection()
        if selection:
            self.on_customer_select(None)
        else:
            self.clear_form()
    
    def delete_customer(self):
        """Delete the selected customer"""
        selection = self.customers_tree.selection()
        if not selection:
            messagebox.showinfo("تنبيه", "الرجاء اختيار عميل أولاً")
            return
        
        customer_id = self.customers_tree.item(selection[0], 'values')[0]
        customer_name = self.customers_tree.item(selection[0], 'values')[1]
        
        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف العميل '{customer_name}'؟"):
            success = self.customer_model.delete_customer(customer_id)
            
            if success:
                messagebox.showinfo("نجاح", "تم حذف العميل بنجاح")
                self.refresh_customers()
                self.clear_form()
                self.set_form_state(tk.DISABLED)
            else:
                messagebox.showerror("خطأ", "فشل حذف العميل")
    
    def add_loyalty_points(self):
        """Add loyalty points to the customer"""
        customer_id = self.customer_id_var.get()
        if not customer_id:
            return
        
        # Ask for points to add
        points = simpledialog.askinteger(
            "إضافة نقاط", 
            "أدخل عدد النقاط المراد إضافتها:",
            minvalue=1
        )
        
        if points:
            current_points = int(self.loyalty_points_var.get())
            new_points = current_points + points
            
            # Update in database
            success = self.customer_model.update_loyalty_points(customer_id, points)
            
            if success:
                # Update in UI
                self.loyalty_points_var.set(str(new_points))
                
                # Update in treeview
                selection = self.customers_tree.selection()
                if selection:
                    values = list(self.customers_tree.item(selection[0], 'values'))
                    values[4] = new_points
                    self.customers_tree.item(selection[0], values=values)
                
                messagebox.showinfo("نجاح", f"تم إضافة {points} نقطة بنجاح")
            else:
                messagebox.showerror("خطأ", "فشل إضافة النقاط")
    
    def redeem_loyalty_points(self):
        """Redeem loyalty points from the customer"""
        customer_id = self.customer_id_var.get()
        if not customer_id:
            return
        
        current_points = int(self.loyalty_points_var.get())
        
        # Ask for points to redeem
        points = simpledialog.askinteger(
            "استبدال نقاط", 
            "أدخل عدد النقاط المراد استبدالها:",
            minvalue=1,
            maxvalue=current_points
        )
        
        if points:
            new_points = current_points - points
            
            # Update in database
            success = self.customer_model.update_loyalty_points(customer_id, -points)
            
            if success:
                # Update in UI
                self.loyalty_points_var.set(str(new_points))
                
                # Update in treeview
                selection = self.customers_tree.selection()
                if selection:
                    values = list(self.customers_tree.item(selection[0], 'values'))
                    values[4] = new_points
                    self.customers_tree.item(selection[0], values=values)
                
                messagebox.showinfo("نجاح", f"تم استبدال {points} نقطة بنجاح")
            else:
                messagebox.showerror("خطأ", "فشل استبدال النقاط")
    
    def search_customers(self):
        """Search customers by name, phone, or email"""
        search_term = self.search_var.get().strip()
        if search_term:
            customers = self.customer_model.search_customers(search_term)
            self.load_customers(customers)
        else:
            self.refresh_customers()
    
    def refresh_customers(self):
        """Refresh the customers list"""
        self.search_var.set("")
        self.load_customers()


# For testing the customers window independently
if __name__ == "__main__":
    import sqlite3
    from database.db_setup import DatabaseSetup
    
    # Setup database
    db = DatabaseSetup()
    db.connect()
    db.setup_tables()
    db.insert_sample_data()
    
    # Create root window
    root = tk.Tk()
    
    # Mock user for testing
    mock_user = (1, 'admin', 'admin123', 'System Administrator', 'admin', 'day', None, None, None, None, 1)
    
    # Create customers window
    customers_window = CustomersWindow(root, db.conn, mock_user)
    
    # Start the main loop
    root.mainloop()
    
    # Close database connection
    db.close()