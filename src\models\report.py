import sqlite3
import datetime
import calendar

class Report:
    def __init__(self, db_connection):
        """Initialize Report model with database connection"""
        self.conn = db_connection
        self.cursor = self.conn.cursor()
    
    def get_daily_sales(self, date=None):
        """Get sales data for a specific day"""
        if date is None:
            date = datetime.datetime.now().strftime("%Y-%m-%d")
        
        start_date = f"{date} 00:00:00"
        end_date = f"{date} 23:59:59"
        
        try:
            self.cursor.execute('''
            SELECT s.*, c.name as customer_name, u.full_name as user_name
            FROM sales s
            LEFT JOIN customers c ON s.customer_id = c.id
            LEFT JOIN users u ON s.user_id = u.id
            WHERE s.sale_date BETWEEN ? AND ?
            ORDER BY s.sale_date
            ''', (start_date, end_date))
            
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error getting daily sales: {e}")
            return []
    
    def get_daily_summary(self, date=None):
        """Get summary of sales for a specific day"""
        if date is None:
            date = datetime.datetime.now().strftime("%Y-%m-%d")
        
        start_date = f"{date} 00:00:00"
        end_date = f"{date} 23:59:59"
        
        try:
            # Get total sales and amount
            self.cursor.execute('''
            SELECT COUNT(*) as sale_count, 
                   SUM(total_amount) as total_sales,
                   SUM(discount_amount) as total_discounts,
                   SUM(tax_amount) as total_taxes
            FROM sales
            WHERE sale_date BETWEEN ? AND ? AND payment_status != 'voided'
            ''', (start_date, end_date))
            
            summary = self.cursor.fetchone()
            
            # Get payment method breakdown
            self.cursor.execute('''
            SELECT payment_method, COUNT(*) as count, SUM(total_amount) as total
            FROM sales
            WHERE sale_date BETWEEN ? AND ? AND payment_status != 'voided'
            GROUP BY payment_method
            ''', (start_date, end_date))
            
            payment_methods = self.cursor.fetchall()
            
            # Get hourly breakdown
            self.cursor.execute('''
            SELECT strftime('%H', sale_date) as hour, 
                   COUNT(*) as sale_count, 
                   SUM(total_amount) as total_sales
            FROM sales
            WHERE sale_date BETWEEN ? AND ? AND payment_status != 'voided'
            GROUP BY hour
            ORDER BY hour
            ''', (start_date, end_date))
            
            hourly_sales = self.cursor.fetchall()
            
            # Get top selling products
            self.cursor.execute('''
            SELECT p.name, SUM(si.quantity) as total_quantity, 
                   SUM(si.total_price) as total_amount
            FROM sale_items si
            JOIN products p ON si.product_id = p.id
            JOIN sales s ON si.sale_id = s.id
            WHERE s.sale_date BETWEEN ? AND ? AND s.payment_status != 'voided'
            GROUP BY si.product_id
            ORDER BY total_quantity DESC
            LIMIT 10
            ''', (start_date, end_date))
            
            top_products = self.cursor.fetchall()
            
            # Get top selling categories
            self.cursor.execute('''
            SELECT p.category, SUM(si.quantity) as total_quantity, 
                   SUM(si.total_price) as total_amount
            FROM sale_items si
            JOIN products p ON si.product_id = p.id
            JOIN sales s ON si.sale_id = s.id
            WHERE s.sale_date BETWEEN ? AND ? AND s.payment_status != 'voided'
            GROUP BY p.category
            ORDER BY total_amount DESC
            ''', (start_date, end_date))
            
            top_categories = self.cursor.fetchall()
            
            # Get staff performance
            self.cursor.execute('''
            SELECT u.full_name, COUNT(s.id) as sale_count, 
                   SUM(s.total_amount) as total_sales
            FROM sales s
            JOIN users u ON s.user_id = u.id
            WHERE s.sale_date BETWEEN ? AND ? AND s.payment_status != 'voided'
            GROUP BY s.user_id
            ORDER BY total_sales DESC
            ''', (start_date, end_date))
            
            staff_performance = self.cursor.fetchall()
            
            return {
                'summary': summary,
                'payment_methods': payment_methods,
                'hourly_sales': hourly_sales,
                'top_products': top_products,
                'top_categories': top_categories,
                'staff_performance': staff_performance
            }
        except sqlite3.Error as e:
            print(f"Error getting daily summary: {e}")
            return None
    
    def get_weekly_summary(self, year=None, week=None):
        """Get summary of sales for a specific week"""
        if year is None or week is None:
            today = datetime.datetime.now()
            year = today.year
            week = today.isocalendar()[1]  # ISO week number
        
        # Calculate start and end dates for the week
        first_day = datetime.datetime.strptime(f'{year}-{week}-1', '%Y-%W-%w')
        last_day = first_day + datetime.timedelta(days=6)
        
        start_date = first_day.strftime("%Y-%m-%d 00:00:00")
        end_date = last_day.strftime("%Y-%m-%d 23:59:59")
        
        try:
            # Get daily breakdown
            self.cursor.execute('''
            SELECT strftime('%Y-%m-%d', sale_date) as day, 
                   COUNT(*) as sale_count, 
                   SUM(total_amount) as total_sales,
                   SUM(discount_amount) as total_discounts,
                   SUM(tax_amount) as total_taxes
            FROM sales
            WHERE sale_date BETWEEN ? AND ? AND payment_status != 'voided'
            GROUP BY day
            ORDER BY day
            ''', (start_date, end_date))
            
            daily_sales = self.cursor.fetchall()
            
            # Get total for the week
            self.cursor.execute('''
            SELECT COUNT(*) as sale_count, 
                   SUM(total_amount) as total_sales,
                   SUM(discount_amount) as total_discounts,
                   SUM(tax_amount) as total_taxes,
                   AVG(total_amount) as average_sale
            FROM sales
            WHERE sale_date BETWEEN ? AND ? AND payment_status != 'voided'
            ''', (start_date, end_date))
            
            weekly_total = self.cursor.fetchone()
            
            # Get payment method breakdown
            self.cursor.execute('''
            SELECT payment_method, COUNT(*) as count, SUM(total_amount) as total
            FROM sales
            WHERE sale_date BETWEEN ? AND ? AND payment_status != 'voided'
            GROUP BY payment_method
            ''', (start_date, end_date))
            
            payment_methods = self.cursor.fetchall()
            
            # Get top selling products
            self.cursor.execute('''
            SELECT p.name, SUM(si.quantity) as total_quantity, 
                   SUM(si.total_price) as total_amount
            FROM sale_items si
            JOIN products p ON si.product_id = p.id
            JOIN sales s ON si.sale_id = s.id
            WHERE s.sale_date BETWEEN ? AND ? AND s.payment_status != 'voided'
            GROUP BY si.product_id
            ORDER BY total_quantity DESC
            LIMIT 10
            ''', (start_date, end_date))
            
            top_products = self.cursor.fetchall()
            
            # Get staff performance
            self.cursor.execute('''
            SELECT u.full_name, COUNT(s.id) as sale_count, 
                   SUM(s.total_amount) as total_sales
            FROM sales s
            JOIN users u ON s.user_id = u.id
            WHERE s.sale_date BETWEEN ? AND ? AND s.payment_status != 'voided'
            GROUP BY s.user_id
            ORDER BY total_sales DESC
            ''', (start_date, end_date))
            
            staff_performance = self.cursor.fetchall()
            
            return {
                'start_date': start_date[:10],
                'end_date': end_date[:10],
                'daily_sales': daily_sales,
                'weekly_total': weekly_total,
                'payment_methods': payment_methods,
                'top_products': top_products,
                'staff_performance': staff_performance
            }
        except sqlite3.Error as e:
            print(f"Error getting weekly summary: {e}")
            return None
    
    def get_monthly_summary(self, year=None, month=None):
        """Get summary of sales for a specific month"""
        if year is None or month is None:
            today = datetime.datetime.now()
            year = today.year
            month = today.month
        
        # Calculate start and end dates for the month
        last_day = calendar.monthrange(year, month)[1]
        start_date = f"{year}-{month:02d}-01 00:00:00"
        end_date = f"{year}-{month:02d}-{last_day} 23:59:59"
        
        try:
            # Get daily breakdown
            self.cursor.execute('''
            SELECT strftime('%Y-%m-%d', sale_date) as day, 
                   COUNT(*) as sale_count, 
                   SUM(total_amount) as total_sales
            FROM sales
            WHERE sale_date BETWEEN ? AND ? AND payment_status != 'voided'
            GROUP BY day
            ORDER BY day
            ''', (start_date, end_date))
            
            daily_sales = self.cursor.fetchall()
            
            # Get total for the month
            self.cursor.execute('''
            SELECT COUNT(*) as sale_count, 
                   SUM(total_amount) as total_sales,
                   SUM(discount_amount) as total_discounts,
                   SUM(tax_amount) as total_taxes,
                   AVG(total_amount) as average_sale
            FROM sales
            WHERE sale_date BETWEEN ? AND ? AND payment_status != 'voided'
            ''', (start_date, end_date))
            
            monthly_total = self.cursor.fetchone()
            
            # Get payment method breakdown
            self.cursor.execute('''
            SELECT payment_method, COUNT(*) as count, SUM(total_amount) as total
            FROM sales
            WHERE sale_date BETWEEN ? AND ? AND payment_status != 'voided'
            GROUP BY payment_method
            ''', (start_date, end_date))
            
            payment_methods = self.cursor.fetchall()
            
            # Get top selling products
            self.cursor.execute('''
            SELECT p.name, SUM(si.quantity) as total_quantity, 
                   SUM(si.total_price) as total_amount
            FROM sale_items si
            JOIN products p ON si.product_id = p.id
            JOIN sales s ON si.sale_id = s.id
            WHERE s.sale_date BETWEEN ? AND ? AND s.payment_status != 'voided'
            GROUP BY si.product_id
            ORDER BY total_quantity DESC
            LIMIT 10
            ''', (start_date, end_date))
            
            top_products = self.cursor.fetchall()
            
            # Get top selling categories
            self.cursor.execute('''
            SELECT p.category, SUM(si.quantity) as total_quantity, 
                   SUM(si.total_price) as total_amount
            FROM sale_items si
            JOIN products p ON si.product_id = p.id
            JOIN sales s ON si.sale_id = s.id
            WHERE s.sale_date BETWEEN ? AND ? AND s.payment_status != 'voided'
            GROUP BY p.category
            ORDER BY total_amount DESC
            ''', (start_date, end_date))
            
            top_categories = self.cursor.fetchall()
            
            # Get staff performance
            self.cursor.execute('''
            SELECT u.full_name, COUNT(s.id) as sale_count, 
                   SUM(s.total_amount) as total_sales
            FROM sales s
            JOIN users u ON s.user_id = u.id
            WHERE s.sale_date BETWEEN ? AND ? AND s.payment_status != 'voided'
            GROUP BY s.user_id
            ORDER BY total_sales DESC
            ''', (start_date, end_date))
            
            staff_performance = self.cursor.fetchall()
            
            # Get weekly breakdown
            self.cursor.execute('''
            SELECT strftime('%W', sale_date) as week, 
                   COUNT(*) as sale_count, 
                   SUM(total_amount) as total_sales
            FROM sales
            WHERE sale_date BETWEEN ? AND ? AND payment_status != 'voided'
            GROUP BY week
            ORDER BY week
            ''', (start_date, end_date))
            
            weekly_sales = self.cursor.fetchall()
            
            return {
                'month_name': calendar.month_name[month],
                'year': year,
                'daily_sales': daily_sales,
                'weekly_sales': weekly_sales,
                'monthly_total': monthly_total,
                'payment_methods': payment_methods,
                'top_products': top_products,
                'top_categories': top_categories,
                'staff_performance': staff_performance
            }
        except sqlite3.Error as e:
            print(f"Error getting monthly summary: {e}")
            return None
    
    def get_inventory_report(self):
        """Get inventory status report"""
        try:
            # Get all products with their current stock
            self.cursor.execute('''
            SELECT p.*, s.name as supplier_name,
                   CASE 
                       WHEN p.quantity <= p.min_quantity THEN 'Low Stock'
                       WHEN p.quantity = 0 THEN 'Out of Stock'
                       ELSE 'In Stock'
                   END as stock_status
            FROM products p
            LEFT JOIN suppliers s ON p.supplier_id = s.id
            ORDER BY p.category, p.name
            ''')
            
            products = self.cursor.fetchall()
            
            # Get low stock products
            self.cursor.execute('''
            SELECT p.*, s.name as supplier_name
            FROM products p
            LEFT JOIN suppliers s ON p.supplier_id = s.id
            WHERE p.quantity <= p.min_quantity
            ORDER BY p.quantity ASC
            ''')
            
            low_stock = self.cursor.fetchall()
            
            # Get stock value
            self.cursor.execute('''
            SELECT SUM(quantity * purchase_price) as total_cost_value,
                   SUM(quantity * selling_price) as total_retail_value,
                   COUNT(*) as total_products,
                   SUM(quantity) as total_items
            FROM products
            ''')
            
            stock_value = self.cursor.fetchone()
            
            # Get category breakdown
            self.cursor.execute('''
            SELECT category, COUNT(*) as product_count,
                   SUM(quantity) as total_quantity,
                   SUM(quantity * purchase_price) as cost_value,
                   SUM(quantity * selling_price) as retail_value
            FROM products
            GROUP BY category
            ORDER BY total_quantity DESC
            ''')
            
            category_breakdown = self.cursor.fetchall()
            
            # Get recent inventory transactions
            self.cursor.execute('''
            SELECT t.*, p.name as product_name, u.full_name as user_name
            FROM inventory_transactions t
            JOIN products p ON t.product_id = p.id
            JOIN users u ON t.user_id = u.id
            ORDER BY t.transaction_date DESC
            LIMIT 50
            ''')
            
            recent_transactions = self.cursor.fetchall()
            
            return {
                'products': products,
                'low_stock': low_stock,
                'stock_value': stock_value,
                'category_breakdown': category_breakdown,
                'recent_transactions': recent_transactions
            }
        except sqlite3.Error as e:
            print(f"Error getting inventory report: {e}")
            return None
    
    def get_customer_report(self):
        """Get customer activity report"""
        try:
            # Get all customers with their purchase stats
            self.cursor.execute('''
            SELECT c.*, 
                   COUNT(s.id) as purchase_count,
                   SUM(s.total_amount) as total_spent,
                   MAX(s.sale_date) as last_purchase_date,
                   AVG(s.total_amount) as average_purchase
            FROM customers c
            LEFT JOIN sales s ON c.id = s.customer_id AND s.payment_status != 'voided'
            GROUP BY c.id
            ORDER BY total_spent DESC
            ''')
            
            customers = self.cursor.fetchall()
            
            # Get top customers by spending
            self.cursor.execute('''
            SELECT c.*, 
                   COUNT(s.id) as purchase_count,
                   SUM(s.total_amount) as total_spent,
                   MAX(s.sale_date) as last_purchase_date,
                   AVG(s.total_amount) as average_purchase
            FROM customers c
            JOIN sales s ON c.id = s.customer_id AND s.payment_status != 'voided'
            GROUP BY c.id
            ORDER BY total_spent DESC
            LIMIT 10
            ''')
            
            top_customers = self.cursor.fetchall()
            
            # Get top customers by frequency
            self.cursor.execute('''
            SELECT c.*, 
                   COUNT(s.id) as purchase_count,
                   SUM(s.total_amount) as total_spent,
                   MAX(s.sale_date) as last_purchase_date,
                   AVG(s.total_amount) as average_purchase
            FROM customers c
            JOIN sales s ON c.id = s.customer_id AND s.payment_status != 'voided'
            GROUP BY c.id
            ORDER BY purchase_count DESC
            LIMIT 10
            ''')
            
            frequent_customers = self.cursor.fetchall()
            
            # Get inactive customers (no purchase in last 30 days)
            thirty_days_ago = (datetime.datetime.now() - datetime.timedelta(days=30)).strftime("%Y-%m-%d")
            
            self.cursor.execute('''
            SELECT c.*, 
                   COUNT(s.id) as purchase_count,
                   SUM(s.total_amount) as total_spent,
                   MAX(s.sale_date) as last_purchase_date
            FROM customers c
            LEFT JOIN sales s ON c.id = s.customer_id AND s.payment_status != 'voided'
            GROUP BY c.id
            HAVING last_purchase_date < ? OR last_purchase_date IS NULL
            ORDER BY last_purchase_date DESC
            ''', (thirty_days_ago,))
            
            inactive_customers = self.cursor.fetchall()
            
            # Get customer summary
            self.cursor.execute('''
            SELECT COUNT(*) as total_customers,
                   SUM(loyalty_points) as total_loyalty_points,
                   (SELECT COUNT(*) FROM customers WHERE last_purchase >= ?) as active_customers
            FROM customers
            ''', (thirty_days_ago,))
            
            customer_summary = self.cursor.fetchone()
            
            return {
                'customers': customers,
                'top_customers': top_customers,
                'frequent_customers': frequent_customers,
                'inactive_customers': inactive_customers,
                'customer_summary': customer_summary
            }
        except sqlite3.Error as e:
            print(f"Error getting customer report: {e}")
            return None
    
    def get_staff_report(self, start_date=None, end_date=None):
        """Get staff performance report"""
        if start_date is None:
            # Default to last 30 days
            start_date = (datetime.datetime.now() - datetime.timedelta(days=30)).strftime("%Y-%m-%d 00:00:00")
        
        if end_date is None:
            end_date = datetime.datetime.now().strftime("%Y-%m-%d 23:59:59")
        
        try:
            # Get staff sales performance
            self.cursor.execute('''
            SELECT u.id, u.full_name, u.role, u.shift,
                   COUNT(s.id) as sale_count,
                   SUM(s.total_amount) as total_sales,
                   AVG(s.total_amount) as average_sale,
                   COUNT(DISTINCT strftime('%Y-%m-%d', s.sale_date)) as days_worked
            FROM users u
            LEFT JOIN sales s ON u.id = s.user_id AND s.payment_status != 'voided'
                AND s.sale_date BETWEEN ? AND ?
            GROUP BY u.id
            ORDER BY total_sales DESC
            ''', (start_date, end_date))
            
            staff_performance = self.cursor.fetchall()
            
            # Get daily breakdown by staff
            self.cursor.execute('''
            SELECT u.full_name, strftime('%Y-%m-%d', s.sale_date) as day,
                   COUNT(s.id) as sale_count,
                   SUM(s.total_amount) as total_sales
            FROM sales s
            JOIN users u ON s.user_id = u.id
            WHERE s.payment_status != 'voided' AND s.sale_date BETWEEN ? AND ?
            GROUP BY u.id, day
            ORDER BY day, u.full_name
            ''', (start_date, end_date))
            
            daily_performance = self.cursor.fetchall()
            
            # Get shift performance
            self.cursor.execute('''
            SELECT u.shift, COUNT(s.id) as sale_count,
                   SUM(s.total_amount) as total_sales,
                   COUNT(DISTINCT u.id) as staff_count,
                   SUM(s.total_amount) / COUNT(DISTINCT u.id) as sales_per_staff
            FROM sales s
            JOIN users u ON s.user_id = u.id
            WHERE s.payment_status != 'voided' AND s.sale_date BETWEEN ? AND ?
            GROUP BY u.shift
            ''', (start_date, end_date))
            
            shift_performance = self.cursor.fetchall()
            
            # Get top selling products by staff
            self.cursor.execute('''
            SELECT u.full_name, p.name as product_name,
                   SUM(si.quantity) as total_quantity,
                   SUM(si.total_price) as total_sales
            FROM sale_items si
            JOIN sales s ON si.sale_id = s.id
            JOIN users u ON s.user_id = u.id
            JOIN products p ON si.product_id = p.id
            WHERE s.payment_status != 'voided' AND s.sale_date BETWEEN ? AND ?
            GROUP BY u.id, si.product_id
            ORDER BY u.full_name, total_quantity DESC
            ''', (start_date, end_date))
            
            staff_products = self.cursor.fetchall()
            
            return {
                'start_date': start_date[:10],
                'end_date': end_date[:10],
                'staff_performance': staff_performance,
                'daily_performance': daily_performance,
                'shift_performance': shift_performance,
                'staff_products': staff_products
            }
        except sqlite3.Error as e:
            print(f"Error getting staff report: {e}")
            return None