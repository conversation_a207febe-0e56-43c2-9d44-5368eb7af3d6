import sqlite3
import datetime

class User:
    def __init__(self, db_connection):
        """Initialize User model with database connection"""
        self.conn = db_connection
        self.cursor = self.conn.cursor()
    
    def get_all_users(self):
        """Get all users from the database"""
        try:
            self.cursor.execute("SELECT * FROM users WHERE active = 1")
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error getting users: {e}")
            return []
    
    def get_user_by_id(self, user_id):
        """Get a user by ID"""
        try:
            self.cursor.execute("SELECT * FROM users WHERE id = ?", (user_id,))
            return self.cursor.fetchone()
        except sqlite3.Error as e:
            print(f"Error getting user by ID: {e}")
            return None
    
    def get_user_by_username(self, username):
        """Get a user by username"""
        try:
            self.cursor.execute("SELECT * FROM users WHERE username = ?", (username,))
            return self.cursor.fetchone()
        except sqlite3.Error as e:
            print(f"Error getting user by username: {e}")
            return None
    
    def authenticate_user(self, username, password):
        """Authenticate a user by username and password"""
        try:
            self.cursor.execute("SELECT * FROM users WHERE username = ? AND password = ? AND active = 1", 
                               (username, password))
            user = self.cursor.fetchone()
            if user:
                # Update last login time
                self.cursor.execute("UPDATE users SET last_login = ? WHERE id = ?", 
                                   (datetime.datetime.now(), user[0]))
                self.conn.commit()
            return user
        except sqlite3.Error as e:
            print(f"Error authenticating user: {e}")
            return None
    
    def add_user(self, username, password, full_name, role, shift, contact=None, email=None):
        """Add a new user to the database"""
        try:
            self.cursor.execute('''
            INSERT INTO users (username, password, full_name, role, shift, contact, email)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (username, password, full_name, role, shift, contact, email))
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"Error adding user: {e}")
            return None
    
    def update_user(self, user_id, username=None, password=None, full_name=None, 
                   role=None, shift=None, contact=None, email=None, active=None):
        """Update an existing user"""
        try:
            # Get current user data
            self.cursor.execute("SELECT * FROM users WHERE id = ?", (user_id,))
            user = self.cursor.fetchone()
            if not user:
                return False
            
            # Use current values if new ones not provided
            username = username if username is not None else user[1]
            password = password if password is not None else user[2]
            full_name = full_name if full_name is not None else user[3]
            role = role if role is not None else user[4]
            shift = shift if shift is not None else user[5]
            contact = contact if contact is not None else user[6]
            email = email if email is not None else user[7]
            active = active if active is not None else user[10]
            
            self.cursor.execute('''
            UPDATE users 
            SET username = ?, password = ?, full_name = ?, role = ?, shift = ?, 
                contact = ?, email = ?, active = ?
            WHERE id = ?
            ''', (username, password, full_name, role, shift, contact, email, active, user_id))
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error updating user: {e}")
            return False
    
    def delete_user(self, user_id):
        """Soft delete a user (set active to 0)"""
        try:
            self.cursor.execute("UPDATE users SET active = 0 WHERE id = ?", (user_id,))
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error deleting user: {e}")
            return False
    
    def get_users_by_shift(self, shift):
        """Get all users for a specific shift"""
        try:
            self.cursor.execute("SELECT * FROM users WHERE shift = ? AND active = 1", (shift,))
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error getting users by shift: {e}")
            return []