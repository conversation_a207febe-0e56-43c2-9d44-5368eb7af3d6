import sqlite3
import datetime
import random
import string

class Sale:
    def __init__(self, db_connection):
        """Initialize Sale model with database connection"""
        self.conn = db_connection
        self.cursor = self.conn.cursor()
    
    def generate_invoice_number(self):
        """Generate a unique invoice number"""
        # Format: INV-YYYYMMDD-XXXX (where XXXX is a random string)
        date_part = datetime.datetime.now().strftime("%Y%m%d")
        random_part = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
        invoice_number = f"INV-{date_part}-{random_part}"
        
        # Check if invoice number already exists
        self.cursor.execute("SELECT COUNT(*) FROM sales WHERE invoice_number = ?", (invoice_number,))
        if self.cursor.fetchone()[0] > 0:
            # If exists, generate a new one recursively
            return self.generate_invoice_number()
        
        return invoice_number
    
    def create_sale(self, user_id, customer_id=None, payment_method="cash", payment_status="paid"):
        """Create a new sale with initial values"""
        try:
            invoice_number = self.generate_invoice_number()
            
            self.cursor.execute('''
            INSERT INTO sales (
                invoice_number, customer_id, user_id, total_amount, 
                payment_method, payment_status
            )
            VALUES (?, ?, ?, ?, ?, ?)
            ''', (invoice_number, customer_id, user_id, 0, payment_method, payment_status))
            self.conn.commit()
            
            sale_id = self.cursor.lastrowid
            return sale_id, invoice_number
        except sqlite3.Error as e:
            print(f"Error creating sale: {e}")
            return None, None
    
    def add_sale_item(self, sale_id, product_id, quantity, unit_price, discount=0):
        """Add an item to a sale"""
        try:
            # Calculate total price
            total_price = (unit_price * quantity) - discount
            
            self.cursor.execute('''
            INSERT INTO sale_items (
                sale_id, product_id, quantity, unit_price, discount, total_price
            )
            VALUES (?, ?, ?, ?, ?, ?)
            ''', (sale_id, product_id, quantity, unit_price, discount, total_price))
            
            # Update product stock
            self.cursor.execute('''
            UPDATE products 
            SET quantity = quantity - ?, last_updated = ?
            WHERE id = ?
            ''', (quantity, datetime.datetime.now(), product_id))
            
            # Get sale user_id
            self.cursor.execute("SELECT user_id FROM sales WHERE id = ?", (sale_id,))
            user_id = self.cursor.fetchone()[0]
            
            # Log inventory transaction
            self.cursor.execute('''
            INSERT INTO inventory_transactions (
                product_id, transaction_type, quantity, user_id, notes
            )
            VALUES (?, ?, ?, ?, ?)
            ''', (product_id, 'sale', -quantity, user_id, f"Sale ID: {sale_id}"))
            
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"Error adding sale item: {e}")
            return None
    
    def update_sale_totals(self, sale_id, discount_amount=0, tax_amount=0):
        """Update the sale totals based on items"""
        try:
            # Calculate total from items
            self.cursor.execute('''
            SELECT SUM(total_price) FROM sale_items WHERE sale_id = ?
            ''', (sale_id,))
            items_total = self.cursor.fetchone()[0] or 0
            
            # Apply discount and tax
            total_amount = items_total - discount_amount + tax_amount
            
            self.cursor.execute('''
            UPDATE sales 
            SET total_amount = ?, discount_amount = ?, tax_amount = ?
            WHERE id = ?
            ''', (total_amount, discount_amount, tax_amount, sale_id))
            
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error updating sale totals: {e}")
            return False
    
    def finalize_sale(self, sale_id, payment_method, payment_status="paid"):
        """Finalize a sale with payment information"""
        try:
            self.cursor.execute('''
            UPDATE sales 
            SET payment_method = ?, payment_status = ?
            WHERE id = ?
            ''', (payment_method, payment_status, sale_id))
            
            # Update customer's last purchase if customer exists
            self.cursor.execute("SELECT customer_id FROM sales WHERE id = ?", (sale_id,))
            customer_id = self.cursor.fetchone()[0]
            
            if customer_id:
                self.cursor.execute('''
                UPDATE customers 
                SET last_purchase = ?
                WHERE id = ?
                ''', (datetime.datetime.now(), customer_id))
                
                # Add loyalty points (1 point per currency unit spent)
                self.cursor.execute("SELECT total_amount FROM sales WHERE id = ?", (sale_id,))
                total_amount = self.cursor.fetchone()[0]
                
                loyalty_points = int(total_amount)
                self.cursor.execute('''
                UPDATE customers 
                SET loyalty_points = loyalty_points + ?
                WHERE id = ?
                ''', (loyalty_points, customer_id))
            
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error finalizing sale: {e}")
            return False
    
    def get_sale_by_id(self, sale_id):
        """Get a sale by ID"""
        try:
            self.cursor.execute('''
            SELECT s.*, c.name as customer_name, u.full_name as user_name
            FROM sales s
            LEFT JOIN customers c ON s.customer_id = c.id
            LEFT JOIN users u ON s.user_id = u.id
            WHERE s.id = ?
            ''', (sale_id,))
            return self.cursor.fetchone()
        except sqlite3.Error as e:
            print(f"Error getting sale by ID: {e}")
            return None
    
    def get_sale_by_invoice(self, invoice_number):
        """Get a sale by invoice number"""
        try:
            self.cursor.execute('''
            SELECT s.*, c.name as customer_name, u.full_name as user_name
            FROM sales s
            LEFT JOIN customers c ON s.customer_id = c.id
            LEFT JOIN users u ON s.user_id = u.id
            WHERE s.invoice_number = ?
            ''', (invoice_number,))
            return self.cursor.fetchone()
        except sqlite3.Error as e:
            print(f"Error getting sale by invoice: {e}")
            return None
    
    def get_sale_items(self, sale_id):
        """Get all items for a sale"""
        try:
            self.cursor.execute('''
            SELECT si.*, p.name as product_name, p.barcode
            FROM sale_items si
            JOIN products p ON si.product_id = p.id
            WHERE si.sale_id = ?
            ''', (sale_id,))
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error getting sale items: {e}")
            return []
    
    def get_sales_by_date_range(self, start_date, end_date):
        """Get all sales within a date range"""
        try:
            self.cursor.execute('''
            SELECT s.*, c.name as customer_name, u.full_name as user_name
            FROM sales s
            LEFT JOIN customers c ON s.customer_id = c.id
            LEFT JOIN users u ON s.user_id = u.id
            WHERE s.sale_date BETWEEN ? AND ?
            ORDER BY s.sale_date DESC
            ''', (start_date, end_date))
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error getting sales by date range: {e}")
            return []
    
    def get_sales_by_user(self, user_id):
        """Get all sales by a specific user"""
        try:
            self.cursor.execute('''
            SELECT s.*, c.name as customer_name, u.full_name as user_name
            FROM sales s
            LEFT JOIN customers c ON s.customer_id = c.id
            LEFT JOIN users u ON s.user_id = u.id
            WHERE s.user_id = ?
            ORDER BY s.sale_date DESC
            ''', (user_id,))
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error getting sales by user: {e}")
            return []
    
    def get_sales_by_customer(self, customer_id):
        """Get all sales for a specific customer"""
        try:
            self.cursor.execute('''
            SELECT s.*, c.name as customer_name, u.full_name as user_name
            FROM sales s
            LEFT JOIN customers c ON s.customer_id = c.id
            LEFT JOIN users u ON s.user_id = u.id
            WHERE s.customer_id = ?
            ORDER BY s.sale_date DESC
            ''', (customer_id,))
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error getting sales by customer: {e}")
            return []
    
    def get_daily_sales_summary(self, date=None):
        """Get a summary of sales for a specific day"""
        if date is None:
            date = datetime.datetime.now().strftime("%Y-%m-%d")
        
        start_date = f"{date} 00:00:00"
        end_date = f"{date} 23:59:59"
        
        try:
            # Get total sales and amount
            self.cursor.execute('''
            SELECT COUNT(*), SUM(total_amount), SUM(discount_amount), SUM(tax_amount)
            FROM sales
            WHERE sale_date BETWEEN ? AND ?
            ''', (start_date, end_date))
            
            summary = self.cursor.fetchone()
            
            # Get payment method breakdown
            self.cursor.execute('''
            SELECT payment_method, COUNT(*), SUM(total_amount)
            FROM sales
            WHERE sale_date BETWEEN ? AND ?
            GROUP BY payment_method
            ''', (start_date, end_date))
            
            payment_methods = self.cursor.fetchall()
            
            # Get top selling products
            self.cursor.execute('''
            SELECT p.name, SUM(si.quantity) as total_quantity, 
                   SUM(si.total_price) as total_amount
            FROM sale_items si
            JOIN products p ON si.product_id = p.id
            JOIN sales s ON si.sale_id = s.id
            WHERE s.sale_date BETWEEN ? AND ?
            GROUP BY si.product_id
            ORDER BY total_quantity DESC
            LIMIT 5
            ''', (start_date, end_date))
            
            top_products = self.cursor.fetchall()
            
            return {
                'summary': summary,
                'payment_methods': payment_methods,
                'top_products': top_products
            }
        except sqlite3.Error as e:
            print(f"Error getting daily sales summary: {e}")
            return None
    
    def void_sale(self, sale_id, user_id, reason):
        """Void a sale and return items to inventory"""
        try:
            # Check if sale exists
            self.cursor.execute("SELECT * FROM sales WHERE id = ?", (sale_id,))
            sale = self.cursor.fetchone()
            if not sale:
                return False
            
            # Get all sale items
            self.cursor.execute("SELECT * FROM sale_items WHERE sale_id = ?", (sale_id,))
            items = self.cursor.fetchall()
            
            # Return items to inventory
            for item in items:
                product_id = item[2]
                quantity = item[3]
                
                # Update product stock
                self.cursor.execute('''
                UPDATE products 
                SET quantity = quantity + ?, last_updated = ?
                WHERE id = ?
                ''', (quantity, datetime.datetime.now(), product_id))
                
                # Log inventory transaction
                self.cursor.execute('''
                INSERT INTO inventory_transactions (
                    product_id, transaction_type, quantity, user_id, notes
                )
                VALUES (?, ?, ?, ?, ?)
                ''', (product_id, 'void', quantity, user_id, f"Voided Sale ID: {sale_id}, Reason: {reason}"))
            
            # Mark sale as voided
            self.cursor.execute('''
            UPDATE sales 
            SET payment_status = 'voided', notes = ?
            WHERE id = ?
            ''', (reason, sale_id))
            
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error voiding sale: {e}")
            return False