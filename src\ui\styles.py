"""
POSMADORA - Modern UI Styles and Themes
This module provides consistent styling across the application
"""

import tkinter as tk
from tkinter import ttk
import os

class AppStyles:
    """Class to manage application styles and themes"""

    # Futuristic Neon Color Palette
    PRIMARY = "#00d4ff"  # Cyber blue
    PRIMARY_DARK = "#0099cc"  # Darker cyber blue
    PRIMARY_LIGHT = "#33ddff"  # Lighter cyber blue
    PRIMARY_GLOW = "#66d4ff"  # Glowing cyber blue

    SECONDARY = "#ff0080"  # Neon pink
    SECONDARY_DARK = "#cc0066"  # Darker neon pink
    SECONDARY_LIGHT = "#ff33a0"  # Lighter neon pink
    SECONDARY_GLOW = "#ff6699"  # Glowing neon pink

    ACCENT = "#00ff88"  # Neon green
    ACCENT_DARK = "#00cc6a"  # Darker neon green
    ACCENT_LIGHT = "#33ffaa"  # Lighter neon green
    ACCENT_GLOW = "#66ffaa"  # Glowing neon green

    TERTIARY = "#ff8800"  # Neon orange
    TERTIARY_DARK = "#cc6600"  # Darker neon orange
    TERTIARY_LIGHT = "#ffaa33"  # Lighter neon orange
    TERTIARY_GLOW = "#ffaa66"  # Glowing neon orange

    QUATERNARY = "#8800ff"  # Neon purple
    QUATERNARY_DARK = "#6600cc"  # Darker neon purple
    QUATERNARY_LIGHT = "#aa33ff"  # Lighter neon purple
    QUATERNARY_GLOW = "#aa66ff"  # Glowing neon purple

    WARNING = "#ffff00"  # Neon yellow for warnings
    DANGER = "#ff0040"  # Neon red for critical actions

    # Dark theme colors
    DARK = "#0a0a0a"  # Deep black
    DARKER = "#000000"  # Pure black
    DARK_SURFACE = "#1a1a1a"  # Dark surface
    DARK_ELEVATED = "#2a2a2a"  # Elevated dark surface

    # Light colors for contrast
    LIGHT = "#f0f0f0"  # Light gray
    LIGHTER = "#ffffff"  # Pure white

    # Text colors
    TEXT_DARK = "#0a0a0a"  # Dark text
    TEXT_LIGHT = "#ffffff"  # Light text
    TEXT_MUTED = "#888888"  # Muted text
    TEXT_NEON = "#00d4ff"  # Neon text
    TEXT_GLOW = "#ffffff"  # Glowing white text

    # Background colors
    BG_MAIN = "#0f0f0f"  # Main dark background
    BG_SECONDARY = "#1a1a1a"  # Secondary dark background
    BG_HIGHLIGHT = "#2a2a2a"  # Highlighted background
    BG_CARD = "#1f1f1f"  # Card background
    BG_GLASS = "#2a2a2a"  # Glass effect background

    # Border colors
    BORDER = "#333333"
    BORDER_GLOW = "#66d4ff"  # Glowing border

    # Status colors
    SUCCESS = "#00ff88"  # Neon green for success
    INFO = "#00d4ff"  # Cyber blue for information
    WARNING = "#ffff00"  # Neon yellow for warnings
    ERROR = "#ff0040"  # Neon red for errors

    # Gradient definitions
    GRADIENT_PRIMARY = ["#00d4ff", "#0099cc", "#006699"]
    GRADIENT_SECONDARY = ["#ff0080", "#cc0066", "#990033"]
    GRADIENT_ACCENT = ["#00ff88", "#00cc6a", "#009944"]
    GRADIENT_DARK = ["#2a2a2a", "#1a1a1a", "#0a0a0a"]
    GRADIENT_RAINBOW = ["#ff0080", "#ff8800", "#ffff00", "#00ff88", "#00d4ff", "#8800ff"]

    # Shadow definitions
    SHADOW_LIGHT = "#cccccc"
    SHADOW_DARK = "#666666"
    SHADOW_NEON = "#66d4ff"
    SHADOW_GLOW = "#ffffff"

    # Font configurations
    FONT_FAMILY = "Segoe UI"
    FONT_FAMILY_ARABIC = "Segoe UI"  # For Arabic text
    FONT_FAMILY_MONO = "Consolas"  # For monospace text
    FONT_SIZE_TINY = 8
    FONT_SIZE_SMALL = 10
    FONT_SIZE_NORMAL = 12
    FONT_SIZE_LARGE = 14
    FONT_SIZE_XLARGE = 18
    FONT_SIZE_XXLARGE = 24
    FONT_SIZE_HUGE = 32

    # Padding and margins
    PADDING_TINY = 2
    PADDING_SMALL = 5
    PADDING_MEDIUM = 10
    PADDING_LARGE = 15
    PADDING_XLARGE = 20
    PADDING_HUGE = 30

    # Border radius (for custom widgets)
    BORDER_RADIUS_SMALL = 4
    BORDER_RADIUS = 8
    BORDER_RADIUS_LARGE = 16
    BORDER_RADIUS_HUGE = 24

    # Animation duration (in milliseconds)
    ANIMATION_DURATION_FAST = 100
    ANIMATION_DURATION = 200
    ANIMATION_DURATION_SLOW = 400
    ANIMATION_DURATION_VERY_SLOW = 800

    # Opacity levels
    OPACITY_DISABLED = 0.3
    OPACITY_MUTED = 0.6
    OPACITY_NORMAL = 1.0
    OPACITY_GLASS = 0.1
    
    @classmethod
    def setup_styles(cls, root):
        """Set up the application styles"""
        # Configure the root window
        root.configure(bg=cls.BG_MAIN)
        
        # Create a style object
        style = ttk.Style(root)
        
        # Configure the theme
        cls._configure_theme(style)
        
        # Configure specific widget styles
        cls._configure_button_styles(style)
        cls._configure_entry_styles(style)
        cls._configure_treeview_styles(style)
        cls._configure_notebook_styles(style)
        cls._configure_frame_styles(style)
        cls._configure_label_styles(style)
        cls._configure_combobox_styles(style)
        cls._configure_progressbar_styles(style)
        cls._configure_scale_styles(style)
        cls._configure_scrollbar_styles(style)
        
        return style
    
    @classmethod
    def _configure_theme(cls, style):
        """Configure the base theme"""
        # Use the 'clam' theme as a base
        style.theme_use('clam')
        
        # Configure common elements
        style.configure('.', 
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL),
                        background=cls.BG_MAIN)
    
    @classmethod
    def _configure_button_styles(cls, style):
        """Configure button styles"""
        # Default button
        style.configure('TButton', 
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL),
                        background=cls.PRIMARY,
                        foreground=cls.TEXT_LIGHT,
                        borderwidth=0,
                        focusthickness=3,
                        focuscolor=cls.PRIMARY_DARK,
                        padding=(cls.PADDING_MEDIUM, cls.PADDING_SMALL))
        
        style.map('TButton',
                  background=[('active', cls.PRIMARY_DARK), 
                              ('disabled', cls.TEXT_MUTED)],
                  foreground=[('disabled', cls.LIGHT)])
        
        # Primary button
        style.configure('Primary.TButton', 
                        background=cls.PRIMARY,
                        foreground=cls.TEXT_LIGHT)
        
        style.map('Primary.TButton',
                  background=[('active', cls.PRIMARY_DARK), 
                              ('disabled', cls.TEXT_MUTED)])
        
        # Secondary button
        style.configure('Secondary.TButton', 
                        background=cls.SECONDARY,
                        foreground=cls.TEXT_LIGHT)
        
        style.map('Secondary.TButton',
                  background=[('active', cls.SECONDARY_DARK), 
                              ('disabled', cls.TEXT_MUTED)])
        
        # Accent button
        style.configure('Accent.TButton', 
                        background=cls.ACCENT,
                        foreground=cls.TEXT_LIGHT)
        
        style.map('Accent.TButton',
                  background=[('active', cls.ACCENT_DARK), 
                              ('disabled', cls.TEXT_MUTED)])
        
        # Warning button
        style.configure('Warning.TButton', 
                        background=cls.WARNING,
                        foreground=cls.TEXT_LIGHT)
        
        style.map('Warning.TButton',
                  background=[('active', '#e67e22'), 
                              ('disabled', cls.TEXT_MUTED)])
        
        # Danger button
        style.configure('Danger.TButton', 
                        background=cls.DANGER,
                        foreground=cls.TEXT_LIGHT)
        
        style.map('Danger.TButton',
                  background=[('active', '#c0392b'), 
                              ('disabled', cls.TEXT_MUTED)])
        
        # Link button (looks like a hyperlink)
        style.configure('Link.TButton', 
                        background=cls.BG_MAIN,
                        foreground=cls.PRIMARY,
                        borderwidth=0,
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL, 'underline'))
        
        style.map('Link.TButton',
                  foreground=[('active', cls.PRIMARY_DARK), 
                              ('disabled', cls.TEXT_MUTED)],
                  background=[('active', cls.BG_MAIN), 
                              ('disabled', cls.BG_MAIN)])
    
    @classmethod
    def _configure_entry_styles(cls, style):
        """Configure entry styles"""
        style.configure('TEntry',
                        fieldbackground=cls.LIGHT,
                        foreground=cls.TEXT_DARK,
                        bordercolor=cls.BORDER,
                        lightcolor=cls.BORDER,
                        darkcolor=cls.BORDER,
                        borderwidth=1,
                        padding=cls.PADDING_SMALL)
        
        style.map('TEntry',
                  fieldbackground=[('disabled', cls.BG_SECONDARY)],
                  foreground=[('disabled', cls.TEXT_MUTED)],
                  bordercolor=[('focus', cls.PRIMARY)])
    
    @classmethod
    def _configure_treeview_styles(cls, style):
        """Configure treeview styles"""
        style.configure('Treeview',
                        background=cls.LIGHT,
                        foreground=cls.TEXT_DARK,
                        rowheight=25,
                        fieldbackground=cls.LIGHT,
                        borderwidth=0,
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL))
        
        style.map('Treeview',
                  background=[('selected', cls.PRIMARY)],
                  foreground=[('selected', cls.TEXT_LIGHT)])
        
        style.configure('Treeview.Heading',
                        background=cls.DARK,
                        foreground=cls.TEXT_LIGHT,
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL, 'bold'),
                        relief='flat',
                        padding=cls.PADDING_SMALL)
        
        style.map('Treeview.Heading',
                  background=[('active', cls.DARKER)])
    
    @classmethod
    def _configure_notebook_styles(cls, style):
        """Configure notebook styles"""
        style.configure('TNotebook',
                        background=cls.BG_MAIN,
                        borderwidth=0)
        
        style.configure('TNotebook.Tab',
                        background=cls.BG_SECONDARY,
                        foreground=cls.TEXT_DARK,
                        padding=(cls.PADDING_MEDIUM, cls.PADDING_SMALL),
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL))
        
        style.map('TNotebook.Tab',
                  background=[('selected', cls.PRIMARY), ('active', cls.BG_HIGHLIGHT)],
                  foreground=[('selected', cls.TEXT_LIGHT), ('active', cls.TEXT_DARK)],
                  expand=[('selected', [1, 1, 1, 0])])
    
    @classmethod
    def _configure_frame_styles(cls, style):
        """Configure frame styles"""
        style.configure('TFrame',
                        background=cls.BG_MAIN)
        
        style.configure('Card.TFrame',
                        background=cls.LIGHT,
                        relief='solid',
                        borderwidth=1,
                        bordercolor=cls.BORDER)
        
        style.configure('Header.TFrame',
                        background=cls.PRIMARY,
                        relief='flat')
        
        style.configure('Footer.TFrame',
                        background=cls.DARK,
                        relief='flat')
    
    @classmethod
    def _configure_label_styles(cls, style):
        """Configure label styles"""
        style.configure('TLabel',
                        background=cls.BG_MAIN,
                        foreground=cls.TEXT_DARK,
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL))
        
        style.configure('Title.TLabel',
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_XLARGE, 'bold'),
                        foreground=cls.PRIMARY)
        
        style.configure('Subtitle.TLabel',
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_LARGE, 'bold'),
                        foreground=cls.TEXT_DARK)
        
        style.configure('Header.TLabel',
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_XXLARGE, 'bold'),
                        foreground=cls.PRIMARY)
        
        style.configure('WhiteTitle.TLabel',
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_XLARGE, 'bold'),
                        foreground=cls.TEXT_LIGHT,
                        background=cls.PRIMARY)
        
        style.configure('WhiteSubtitle.TLabel',
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_LARGE, 'bold'),
                        foreground=cls.TEXT_LIGHT,
                        background=cls.PRIMARY)
        
        style.configure('Info.TLabel',
                        foreground=cls.INFO)
        
        style.configure('Success.TLabel',
                        foreground=cls.SUCCESS)
        
        style.configure('Warning.TLabel',
                        foreground=cls.WARNING)
        
        style.configure('Error.TLabel',
                        foreground=cls.ERROR)
    
    @classmethod
    def _configure_combobox_styles(cls, style):
        """Configure combobox styles"""
        style.configure('TCombobox',
                        fieldbackground=cls.LIGHT,
                        background=cls.PRIMARY,
                        foreground=cls.TEXT_DARK,
                        arrowcolor=cls.PRIMARY,
                        bordercolor=cls.BORDER,
                        lightcolor=cls.BORDER,
                        darkcolor=cls.BORDER,
                        padding=cls.PADDING_SMALL)
        
        style.map('TCombobox',
                  fieldbackground=[('readonly', cls.LIGHT), ('disabled', cls.BG_SECONDARY)],
                  foreground=[('readonly', cls.TEXT_DARK), ('disabled', cls.TEXT_MUTED)],
                  selectbackground=[('readonly', cls.PRIMARY)],
                  selectforeground=[('readonly', cls.TEXT_LIGHT)])
    
    @classmethod
    def _configure_progressbar_styles(cls, style):
        """Configure progressbar styles"""
        style.configure('TProgressbar',
                        troughcolor=cls.BG_SECONDARY,
                        background=cls.PRIMARY,
                        borderwidth=0)
        
        style.configure('Success.TProgressbar',
                        background=cls.SUCCESS)
        
        style.configure('Warning.TProgressbar',
                        background=cls.WARNING)
        
        style.configure('Danger.TProgressbar',
                        background=cls.DANGER)
    
    @classmethod
    def _configure_scale_styles(cls, style):
        """Configure scale styles"""
        style.configure('TScale',
                        troughcolor=cls.BG_SECONDARY,
                        background=cls.PRIMARY,
                        borderwidth=0)
    
    @classmethod
    def _configure_scrollbar_styles(cls, style):
        """Configure scrollbar styles"""
        style.configure('TScrollbar',
                        troughcolor=cls.BG_SECONDARY,
                        background=cls.PRIMARY,
                        borderwidth=0,
                        arrowcolor=cls.TEXT_LIGHT)
        
        style.map('TScrollbar',
                  background=[('active', cls.PRIMARY_DARK), 
                              ('disabled', cls.TEXT_MUTED)])

# Custom widgets
class RoundedButton(tk.Canvas):
    """A custom rounded button widget"""
    def __init__(self, parent, text="Button", command=None, radius=10, 
                 bg_color=AppStyles.PRIMARY, fg_color=AppStyles.TEXT_LIGHT, 
                 hover_color=AppStyles.PRIMARY_DARK, width=120, height=40, 
                 font=(AppStyles.FONT_FAMILY, AppStyles.FONT_SIZE_NORMAL), **kwargs):
        super().__init__(parent, width=width, height=height, 
                         bg=parent["bg"], highlightthickness=0, **kwargs)
        
        self.radius = radius
        self.bg_color = bg_color
        self.fg_color = fg_color
        self.hover_color = hover_color
        self.current_color = bg_color
        self.command = command
        self.text = text
        self.font = font
        
        # Draw the initial button
        self._draw_button()
        
        # Bind events
        self.bind("<Enter>", self._on_enter)
        self.bind("<Leave>", self._on_leave)
        self.bind("<Button-1>", self._on_click)
        self.bind("<ButtonRelease-1>", self._on_release)
    
    def _draw_button(self):
        """Draw the button on the canvas"""
        self.delete("all")
        
        # Draw the rounded rectangle
        self.create_rounded_rect(0, 0, self.winfo_width(), self.winfo_height(), 
                                 self.radius, fill=self.current_color, outline="")
        
        # Draw the text
        self.create_text(self.winfo_width() / 2, self.winfo_height() / 2, 
                         text=self.text, fill=self.fg_color, font=self.font)
    
    def create_rounded_rect(self, x1, y1, x2, y2, radius, **kwargs):
        """Create a rounded rectangle"""
        points = [
            x1 + radius, y1,
            x2 - radius, y1,
            x2, y1,
            x2, y1 + radius,
            x2, y2 - radius,
            x2, y2,
            x2 - radius, y2,
            x1 + radius, y2,
            x1, y2,
            x1, y2 - radius,
            x1, y1 + radius,
            x1, y1
        ]
        
        return self.create_polygon(points, **kwargs, smooth=True)
    
    def _on_enter(self, event):
        """Handle mouse enter event"""
        self.current_color = self.hover_color
        self._draw_button()
    
    def _on_leave(self, event):
        """Handle mouse leave event"""
        self.current_color = self.bg_color
        self._draw_button()
    
    def _on_click(self, event):
        """Handle mouse click event"""
        self.current_color = self.bg_color
        self._draw_button()
    
    def _on_release(self, event):
        """Handle mouse release event"""
        self.current_color = self.hover_color
        self._draw_button()
        if self.command:
            self.command()
    
    def configure(self, **kwargs):
        """Configure the button properties"""
        if "text" in kwargs:
            self.text = kwargs.pop("text")
        if "bg_color" in kwargs:
            self.bg_color = kwargs.pop("bg_color")
            self.current_color = self.bg_color
        if "fg_color" in kwargs:
            self.fg_color = kwargs.pop("fg_color")
        if "hover_color" in kwargs:
            self.hover_color = kwargs.pop("hover_color")
        if "command" in kwargs:
            self.command = kwargs.pop("command")
        if "font" in kwargs:
            self.font = kwargs.pop("font")
        
        super().configure(**kwargs)
        self._draw_button()

class GradientFrame(tk.Canvas):
    """A frame with a gradient background"""
    def __init__(self, parent, color1=AppStyles.PRIMARY, color2=AppStyles.PRIMARY_DARK, 
                 direction="horizontal", **kwargs):
        super().__init__(parent, **kwargs)
        self.color1 = color1
        self.color2 = color2
        self.direction = direction
        
        self.bind("<Configure>", self._draw_gradient)
    
    def _draw_gradient(self, event=None):
        """Draw the gradient background"""
        self.delete("gradient")
        width = self.winfo_width()
        height = self.winfo_height()
        
        # Create gradient
        if self.direction == "horizontal":
            for i in range(width):
                # Calculate color for this line
                r1, g1, b1 = self.winfo_rgb(self.color1)
                r2, g2, b2 = self.winfo_rgb(self.color2)
                
                # Convert to 8-bit values
                r1, g1, b1 = r1 >> 8, g1 >> 8, b1 >> 8
                r2, g2, b2 = r2 >> 8, g2 >> 8, b2 >> 8
                
                # Calculate the color for this position
                ratio = i / width
                r = int(r1 * (1 - ratio) + r2 * ratio)
                g = int(g1 * (1 - ratio) + g2 * ratio)
                b = int(b1 * (1 - ratio) + b2 * ratio)
                
                color = f"#{r:02x}{g:02x}{b:02x}"
                self.create_line(i, 0, i, height, tags=("gradient",), fill=color)
        else:  # vertical
            for i in range(height):
                # Calculate color for this line
                r1, g1, b1 = self.winfo_rgb(self.color1)
                r2, g2, b2 = self.winfo_rgb(self.color2)
                
                # Convert to 8-bit values
                r1, g1, b1 = r1 >> 8, g1 >> 8, b1 >> 8
                r2, g2, b2 = r2 >> 8, g2 >> 8, b2 >> 8
                
                # Calculate the color for this position
                ratio = i / height
                r = int(r1 * (1 - ratio) + r2 * ratio)
                g = int(g1 * (1 - ratio) + g2 * ratio)
                b = int(b1 * (1 - ratio) + b2 * ratio)
                
                color = f"#{r:02x}{g:02x}{b:02x}"
                self.create_line(0, i, width, i, tags=("gradient",), fill=color)

class AnimatedProgressbar(ttk.Frame):
    """A progressbar with animation effects"""
    def __init__(self, parent, maximum=100, value=0, **kwargs):
        super().__init__(parent, **kwargs)
        
        self.maximum = maximum
        self.current_value = value
        self.target_value = value
        
        # Create the progressbar
        self.progressbar = ttk.Progressbar(self, orient="horizontal", 
                                          length=200, mode="determinate", 
                                          maximum=maximum, value=value)
        self.progressbar.pack(fill=tk.X, expand=True)
        
        # Create the value label
        self.value_label = ttk.Label(self, text=f"{value}%")
        self.value_label.pack(pady=(5, 0))
        
        # Animation variables
        self.animation_id = None
        self.animation_step = 1
    
    def set(self, value):
        """Set the progressbar value with animation"""
        if value == self.current_value:
            return
        
        self.target_value = value
        
        # Cancel any existing animation
        if self.animation_id:
            self.after_cancel(self.animation_id)
        
        # Start the animation
        self._animate()
    
    def _animate(self):
        """Animate the progressbar value change"""
        if self.current_value < self.target_value:
            self.current_value = min(self.current_value + self.animation_step, self.target_value)
        else:
            self.current_value = max(self.current_value - self.animation_step, self.target_value)
        
        # Update the progressbar and label
        self.progressbar["value"] = self.current_value
        self.value_label["text"] = f"{int(self.current_value)}%"
        
        # Continue the animation if needed
        if self.current_value != self.target_value:
            self.animation_id = self.after(10, self._animate)
        else:
            self.animation_id = None

class ToastNotification:
    """A toast notification that appears and disappears automatically"""
    def __init__(self, parent, message, type_="info", duration=3000):
        self.parent = parent
        self.message = message
        self.type = type_
        self.duration = duration
        
        # Create the toast window
        self.toast = tk.Toplevel(parent)
        self.toast.overrideredirect(True)
        self.toast.attributes("-topmost", True)
        
        # Set background color based on type
        if type_ == "info":
            bg_color = AppStyles.INFO
        elif type_ == "success":
            bg_color = AppStyles.SUCCESS
        elif type_ == "warning":
            bg_color = AppStyles.WARNING
        elif type_ == "error":
            bg_color = AppStyles.ERROR
        else:
            bg_color = AppStyles.DARK
        
        # Create the frame
        self.frame = tk.Frame(self.toast, bg=bg_color, padx=15, pady=10)
        self.frame.pack(fill=tk.BOTH, expand=True)
        
        # Create the message label
        self.label = tk.Label(self.frame, text=message, bg=bg_color, 
                             fg=AppStyles.TEXT_LIGHT, font=(AppStyles.FONT_FAMILY, 12))
        self.label.pack(padx=5, pady=5)
        
        # Position the toast at the bottom right of the parent
        self.toast.update_idletasks()
        parent_x = parent.winfo_rootx()
        parent_y = parent.winfo_rooty()
        parent_width = parent.winfo_width()
        parent_height = parent.winfo_height()
        
        toast_width = self.toast.winfo_width()
        toast_height = self.toast.winfo_height()
        
        x = parent_x + parent_width - toast_width - 20
        y = parent_y + parent_height - toast_height - 20
        
        self.toast.geometry(f"+{x}+{y}")
        
        # Schedule the toast to disappear
        self.parent.after(duration, self.destroy)
    
    def destroy(self):
        """Destroy the toast notification"""
        self.toast.destroy()

# Helper functions
def create_tooltip(widget, text):
    """Create a tooltip for a widget"""
    def enter(event):
        x, y, _, _ = widget.bbox("insert")
        x += widget.winfo_rootx() + 25
        y += widget.winfo_rooty() + 25
        
        # Create a toplevel window
        tooltip = tk.Toplevel(widget)
        tooltip.wm_overrideredirect(True)
        tooltip.wm_geometry(f"+{x}+{y}")
        
        # Create a label
        label = tk.Label(tooltip, text=text, justify=tk.LEFT,
                        background=AppStyles.DARK, foreground=AppStyles.TEXT_LIGHT,
                        relief=tk.SOLID, borderwidth=1,
                        font=(AppStyles.FONT_FAMILY, AppStyles.FONT_SIZE_SMALL))
        label.pack(padx=5, pady=5)
        
        widget.tooltip = tooltip
    
    def leave(event):
        if hasattr(widget, "tooltip"):
            widget.tooltip.destroy()
    
    widget.bind("<Enter>", enter)
    widget.bind("<Leave>", leave)

def load_image(path, width=None, height=None):
    """Load an image and optionally resize it"""
    try:
        from PIL import Image, ImageTk
        
        # Check if the file exists
        if not os.path.exists(path):
            return None
        
        # Open the image
        image = Image.open(path)
        
        # Resize if needed
        if width and height:
            image = image.resize((width, height), Image.LANCZOS)
        
        # Convert to PhotoImage
        photo = ImageTk.PhotoImage(image)
        return photo
    except Exception as e:
        print(f"Error loading image: {e}")
        return None

def center_window(window, width=None, height=None):
    """Center a window on the screen"""
    if width and height:
        window.update_idletasks()
        
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        window.geometry(f"{width}x{height}+{x}+{y}")
    else:
        window.update_idletasks()
        
        width = window.winfo_width()
        height = window.winfo_height()
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        window.geometry(f"+{x}+{y}")

def create_rounded_frame(parent, bg_color=AppStyles.LIGHT, corner_radius=10, **kwargs):
    """Create a frame with rounded corners"""
    frame = tk.Frame(parent, bg=parent["bg"], highlightthickness=0, **kwargs)
    
    def _update_canvas(event):
        canvas.delete("all")
        canvas.create_rounded_rectangle(0, 0, event.width, event.height, 
                                       radius=corner_radius, fill=bg_color)
    
    canvas = tk.Canvas(frame, bg=parent["bg"], highlightthickness=0)
    canvas.pack(fill=tk.BOTH, expand=True)
    
    # Create a rounded rectangle on the canvas
    canvas.create_rounded_rectangle = lambda x1, y1, x2, y2, radius, **kwargs: \
        canvas.create_polygon(
            x1 + radius, y1,
            x2 - radius, y1,
            x2, y1,
            x2, y1 + radius,
            x2, y2 - radius,
            x2, y2,
            x2 - radius, y2,
            x1 + radius, y2,
            x1, y2,
            x1, y2 - radius,
            x1, y1 + radius,
            x1, y1,
            smooth=True, **kwargs
        )
    
    canvas.bind("<Configure>", _update_canvas)
    
    # Create an inner frame for content
    inner_frame = tk.Frame(canvas, bg=bg_color, highlightthickness=0)
    canvas.create_window(0, 0, anchor="nw", window=inner_frame, 
                        width=frame.winfo_reqwidth(), 
                        height=frame.winfo_reqheight())
    
    # Update the inner frame size when the canvas changes
    def _update_inner_frame(event):
        canvas.itemconfig("inner_window", width=event.width, height=event.height)
    
    canvas.bind("<Configure>", lambda event: (_update_canvas(event), _update_inner_frame(event)))
    
    return frame, inner_frame

class NeonCard(tk.Frame):
    """A futuristic card with neon glow effects"""
    def __init__(self, parent, title="", content="", glow_color=AppStyles.PRIMARY_GLOW,
                 bg_color=AppStyles.BG_CARD, **kwargs):
        super().__init__(parent, bg=parent["bg"], **kwargs)

        self.title = title
        self.content = content
        self.glow_color = glow_color
        self.bg_color = bg_color

        # Create the main canvas for the card
        self.canvas = tk.Canvas(self, bg=parent["bg"], highlightthickness=0)
        self.canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create the card frame
        self.card_frame = tk.Frame(self.canvas, bg=bg_color, padx=20, pady=15)

        # Add title if provided
        if title:
            self.title_label = tk.Label(self.card_frame, text=title,
                                       font=(AppStyles.FONT_FAMILY, AppStyles.FONT_SIZE_LARGE, 'bold'),
                                       fg=AppStyles.TEXT_NEON, bg=bg_color)
            self.title_label.pack(anchor=tk.W, pady=(0, 10))

        # Add content if provided
        if content:
            self.content_label = tk.Label(self.card_frame, text=content,
                                         font=(AppStyles.FONT_FAMILY, AppStyles.FONT_SIZE_NORMAL),
                                         fg=AppStyles.TEXT_LIGHT, bg=bg_color,
                                         wraplength=300, justify=tk.LEFT)
            self.content_label.pack(anchor=tk.W)

        # Place the card frame in the canvas
        self.canvas_window = self.canvas.create_window(5, 5, anchor=tk.NW, window=self.card_frame)

        # Bind events for glow effect
        self.bind("<Enter>", self._on_enter)
        self.bind("<Leave>", self._on_leave)
        self.canvas.bind("<Enter>", self._on_enter)
        self.canvas.bind("<Leave>", self._on_leave)
        self.card_frame.bind("<Enter>", self._on_enter)
        self.card_frame.bind("<Leave>", self._on_leave)

        # Initial draw
        self.after(100, self._draw_card)

    def _draw_card(self):
        """Draw the card with glow effect"""
        self.canvas.delete("glow")

        # Get card dimensions
        self.update_idletasks()
        card_width = self.card_frame.winfo_reqwidth()
        card_height = self.card_frame.winfo_reqheight()

        # Update canvas size
        self.canvas.configure(width=card_width + 10, height=card_height + 10)

        # Draw glow effect (multiple rectangles with decreasing opacity)
        for i in range(5):
            offset = i * 2
            self.canvas.create_rectangle(5 - offset, 5 - offset,
                                       card_width + 5 + offset, card_height + 5 + offset,
                                       outline=self.glow_color, width=1, tags="glow")

    def _on_enter(self, event):
        """Handle mouse enter - enhance glow"""
        self.glow_color = AppStyles.PRIMARY
        self._draw_card()

    def _on_leave(self, event):
        """Handle mouse leave - reduce glow"""
        self.glow_color = AppStyles.PRIMARY_GLOW
        self._draw_card()

class HolographicButton(tk.Canvas):
    """A holographic-style button with animated effects"""
    def __init__(self, parent, text="Button", command=None, width=150, height=50,
                 primary_color=AppStyles.PRIMARY, secondary_color=AppStyles.SECONDARY, **kwargs):
        super().__init__(parent, width=width, height=height, bg=parent["bg"],
                         highlightthickness=0, **kwargs)

        self.text = text
        self.command = command
        self.primary_color = primary_color
        self.secondary_color = secondary_color
        self.is_hovered = False
        self.animation_frame = 0

        # Bind events
        self.bind("<Enter>", self._on_enter)
        self.bind("<Leave>", self._on_leave)
        self.bind("<Button-1>", self._on_click)

        # Start animation
        self._animate()

    def _animate(self):
        """Animate the holographic effect"""
        self.delete("all")

        # Create gradient background
        for i in range(self.winfo_width()):
            # Calculate color based on position and animation frame
            ratio = (i + self.animation_frame) % self.winfo_width() / self.winfo_width()

            # Interpolate between primary and secondary colors
            r1, g1, b1 = self.winfo_rgb(self.primary_color)
            r2, g2, b2 = self.winfo_rgb(self.secondary_color)

            r1, g1, b1 = r1 >> 8, g1 >> 8, b1 >> 8
            r2, g2, b2 = r2 >> 8, g2 >> 8, b2 >> 8

            r = int(r1 * (1 - ratio) + r2 * ratio)
            g = int(g1 * (1 - ratio) + g2 * ratio)
            b = int(b1 * (1 - ratio) + b2 * ratio)

            color = f"#{r:02x}{g:02x}{b:02x}"
            self.create_line(i, 0, i, self.winfo_height(), fill=color, width=1)

        # Add border glow
        if self.is_hovered:
            self.create_rectangle(2, 2, self.winfo_width()-2, self.winfo_height()-2,
                                outline=AppStyles.TEXT_GLOW, width=2)

        # Add text
        self.create_text(self.winfo_width()/2, self.winfo_height()/2,
                        text=self.text, fill=AppStyles.TEXT_GLOW,
                        font=(AppStyles.FONT_FAMILY, AppStyles.FONT_SIZE_NORMAL, 'bold'))

        # Continue animation
        self.animation_frame = (self.animation_frame + 2) % self.winfo_width()
        self.after(50, self._animate)

    def _on_enter(self, event):
        """Handle mouse enter"""
        self.is_hovered = True
        self.configure(cursor="hand2")

    def _on_leave(self, event):
        """Handle mouse leave"""
        self.is_hovered = False
        self.configure(cursor="")

    def _on_click(self, event):
        """Handle mouse click"""
        if self.command:
            self.command()

class ParticleEffect(tk.Canvas):
    """A particle effect widget for background animations"""
    def __init__(self, parent, particle_count=50, **kwargs):
        super().__init__(parent, **kwargs)

        self.particle_count = particle_count
        self.particles = []

        # Initialize particles
        for _ in range(particle_count):
            particle = {
                'x': random.randint(0, self.winfo_width() or 800),
                'y': random.randint(0, self.winfo_height() or 600),
                'vx': random.uniform(-1, 1),
                'vy': random.uniform(-1, 1),
                'size': random.randint(1, 3),
                'color': random.choice([AppStyles.PRIMARY, AppStyles.SECONDARY,
                                      AppStyles.ACCENT, AppStyles.TERTIARY])
            }
            self.particles.append(particle)

        # Start animation
        self._animate_particles()

    def _animate_particles(self):
        """Animate the particles"""
        self.delete("particle")

        width = self.winfo_width() or 800
        height = self.winfo_height() or 600

        for particle in self.particles:
            # Update position
            particle['x'] += particle['vx']
            particle['y'] += particle['vy']

            # Wrap around edges
            if particle['x'] < 0:
                particle['x'] = width
            elif particle['x'] > width:
                particle['x'] = 0

            if particle['y'] < 0:
                particle['y'] = height
            elif particle['y'] > height:
                particle['y'] = 0

            # Draw particle
            self.create_oval(particle['x'] - particle['size'],
                           particle['y'] - particle['size'],
                           particle['x'] + particle['size'],
                           particle['y'] + particle['size'],
                           fill=particle['color'], outline="", tags="particle")

        # Continue animation
        self.after(50, self._animate_particles)

class GlassPanel(tk.Frame):
    """A glass-effect panel with transparency and blur simulation"""
    def __init__(self, parent, **kwargs):
        super().__init__(parent, bg=AppStyles.BG_GLASS, **kwargs)

        # Create a canvas for the glass effect
        self.canvas = tk.Canvas(self, bg=AppStyles.BG_GLASS, highlightthickness=0)
        self.canvas.pack(fill=tk.BOTH, expand=True)

        # Bind resize event
        self.canvas.bind("<Configure>", self._draw_glass_effect)

    def _draw_glass_effect(self, event=None):
        """Draw the glass effect"""
        self.canvas.delete("glass")

        width = self.canvas.winfo_width()
        height = self.canvas.winfo_height()

        # Create a subtle border
        self.canvas.create_rectangle(1, 1, width-1, height-1,
                                   outline=AppStyles.BORDER_GLOW, width=1, tags="glass")

        # Add some highlight lines for glass effect
        self.canvas.create_line(5, 5, width-5, 5, fill=AppStyles.SHADOW_LIGHT, width=1, tags="glass")
        self.canvas.create_line(5, 5, 5, height-5, fill=AppStyles.SHADOW_LIGHT, width=1, tags="glass")

class CyberProgressBar(tk.Canvas):
    """A cyberpunk-style progress bar with neon effects"""
    def __init__(self, parent, width=300, height=20, maximum=100, **kwargs):
        super().__init__(parent, width=width, height=height, bg=AppStyles.BG_MAIN,
                         highlightthickness=0, **kwargs)

        self.maximum = maximum
        self.current_value = 0
        self.target_value = 0

        # Animation variables
        self.animation_id = None
        self.glow_phase = 0

        # Start glow animation
        self._animate_glow()

    def set(self, value):
        """Set the progress value with animation"""
        self.target_value = min(max(0, value), self.maximum)

        # Cancel existing animation
        if self.animation_id:
            self.after_cancel(self.animation_id)

        # Start value animation
        self._animate_value()

    def _animate_value(self):
        """Animate the progress value"""
        if abs(self.current_value - self.target_value) > 0.5:
            diff = self.target_value - self.current_value
            self.current_value += diff * 0.1
            self.animation_id = self.after(20, self._animate_value)
        else:
            self.current_value = self.target_value
            self.animation_id = None

        self._draw_progress()

    def _animate_glow(self):
        """Animate the glow effect"""
        self.glow_phase = (self.glow_phase + 0.1) % (2 * 3.14159)
        self._draw_progress()
        self.after(50, self._animate_glow)

    def _draw_progress(self):
        """Draw the progress bar"""
        self.delete("all")

        width = self.winfo_width()
        height = self.winfo_height()

        # Draw background
        self.create_rectangle(0, 0, width, height, fill=AppStyles.DARK_SURFACE, outline="")

        # Calculate progress width
        progress_width = (self.current_value / self.maximum) * width

        if progress_width > 0:
            # Draw progress fill with gradient effect
            for i in range(int(progress_width)):
                ratio = i / width
                # Create a color that shifts based on position and glow phase
                glow_intensity = 0.5 + 0.5 * math.sin(self.glow_phase + ratio * 3.14159)

                # Interpolate between primary and accent colors
                r1, g1, b1 = self.winfo_rgb(AppStyles.PRIMARY)
                r2, g2, b2 = self.winfo_rgb(AppStyles.ACCENT)

                r1, g1, b1 = r1 >> 8, g1 >> 8, b1 >> 8
                r2, g2, b2 = r2 >> 8, g2 >> 8, b2 >> 8

                r = int((r1 * (1 - ratio) + r2 * ratio) * glow_intensity)
                g = int((g1 * (1 - ratio) + g2 * ratio) * glow_intensity)
                b = int((b1 * (1 - ratio) + b2 * ratio) * glow_intensity)

                color = f"#{r:02x}{g:02x}{b:02x}"
                self.create_line(i, 0, i, height, fill=color, width=1)

        # Draw border
        self.create_rectangle(0, 0, width, height, outline=AppStyles.BORDER_GLOW, width=1)

# Import required modules for particle effects
import random
import math

# Export the styles
__all__ = ['AppStyles', 'RoundedButton', 'GradientFrame', 'AnimatedProgressbar',
           'ToastNotification', 'NeonCard', 'HolographicButton', 'ParticleEffect',
           'GlassPanel', 'CyberProgressBar', 'create_tooltip', 'load_image',
           'center_window', 'create_rounded_frame']