"""
POSMADORA - Modern UI Styles and Themes
This module provides consistent styling across the application
"""

import tkinter as tk
from tkinter import ttk
import os

class AppStyles:
    """Class to manage application styles and themes"""
    
    # Main color palette
    PRIMARY = "#3498db"  # Main blue color
    PRIMARY_DARK = "#2980b9"  # Darker blue for hover/active states
    SECONDARY = "#2ecc71"  # Green for positive actions
    SECONDARY_DARK = "#27ae60"  # Darker green
    ACCENT = "#9b59b6"  # Purple for highlights
    ACCENT_DARK = "#8e44ad"  # Darker purple
    WARNING = "#f39c12"  # Orange for warnings
    DANGER = "#e74c3c"  # Red for critical actions
    
    # Neutral colors
    DARK = "#34495e"  # Dark blue-gray
    DARKER = "#2c3e50"  # Darker blue-gray
    LIGHT = "#ecf0f1"  # Light gray
    LIGHTER = "#f9f9f9"  # Almost white
    
    # Text colors
    TEXT_DARK = "#2c3e50"  # Dark text
    TEXT_LIGHT = "#ecf0f1"  # Light text
    TEXT_MUTED = "#95a5a6"  # Muted/secondary text
    
    # Background colors
    BG_MAIN = "#f9f9f9"  # Main background
    BG_SECONDARY = "#ecf0f1"  # Secondary background
    BG_HIGHLIGHT = "#e8f4fc"  # Highlighted background
    
    # Border colors
    BORDER = "#bdc3c7"
    
    # Status colors
    SUCCESS = "#2ecc71"  # Green for success
    INFO = "#3498db"  # Blue for information
    WARNING = "#f39c12"  # Orange for warnings
    ERROR = "#e74c3c"  # Red for errors
    
    # Font configurations
    FONT_FAMILY = "Arial"
    FONT_FAMILY_ARABIC = "Arial"  # For Arabic text
    FONT_SIZE_SMALL = 10
    FONT_SIZE_NORMAL = 12
    FONT_SIZE_LARGE = 14
    FONT_SIZE_XLARGE = 18
    FONT_SIZE_XXLARGE = 24
    
    # Padding and margins
    PADDING_SMALL = 5
    PADDING_MEDIUM = 10
    PADDING_LARGE = 15
    PADDING_XLARGE = 20
    
    # Border radius (for custom widgets)
    BORDER_RADIUS = 8
    
    # Animation duration (in milliseconds)
    ANIMATION_DURATION = 150
    
    @classmethod
    def setup_styles(cls, root):
        """Set up the application styles"""
        # Configure the root window
        root.configure(bg=cls.BG_MAIN)
        
        # Create a style object
        style = ttk.Style(root)
        
        # Configure the theme
        cls._configure_theme(style)
        
        # Configure specific widget styles
        cls._configure_button_styles(style)
        cls._configure_entry_styles(style)
        cls._configure_treeview_styles(style)
        cls._configure_notebook_styles(style)
        cls._configure_frame_styles(style)
        cls._configure_label_styles(style)
        cls._configure_combobox_styles(style)
        cls._configure_progressbar_styles(style)
        cls._configure_scale_styles(style)
        cls._configure_scrollbar_styles(style)
        
        return style
    
    @classmethod
    def _configure_theme(cls, style):
        """Configure the base theme"""
        # Use the 'clam' theme as a base
        style.theme_use('clam')
        
        # Configure common elements
        style.configure('.', 
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL),
                        background=cls.BG_MAIN)
    
    @classmethod
    def _configure_button_styles(cls, style):
        """Configure button styles"""
        # Default button
        style.configure('TButton', 
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL),
                        background=cls.PRIMARY,
                        foreground=cls.TEXT_LIGHT,
                        borderwidth=0,
                        focusthickness=3,
                        focuscolor=cls.PRIMARY_DARK,
                        padding=(cls.PADDING_MEDIUM, cls.PADDING_SMALL))
        
        style.map('TButton',
                  background=[('active', cls.PRIMARY_DARK), 
                              ('disabled', cls.TEXT_MUTED)],
                  foreground=[('disabled', cls.LIGHT)])
        
        # Primary button
        style.configure('Primary.TButton', 
                        background=cls.PRIMARY,
                        foreground=cls.TEXT_LIGHT)
        
        style.map('Primary.TButton',
                  background=[('active', cls.PRIMARY_DARK), 
                              ('disabled', cls.TEXT_MUTED)])
        
        # Secondary button
        style.configure('Secondary.TButton', 
                        background=cls.SECONDARY,
                        foreground=cls.TEXT_LIGHT)
        
        style.map('Secondary.TButton',
                  background=[('active', cls.SECONDARY_DARK), 
                              ('disabled', cls.TEXT_MUTED)])
        
        # Accent button
        style.configure('Accent.TButton', 
                        background=cls.ACCENT,
                        foreground=cls.TEXT_LIGHT)
        
        style.map('Accent.TButton',
                  background=[('active', cls.ACCENT_DARK), 
                              ('disabled', cls.TEXT_MUTED)])
        
        # Warning button
        style.configure('Warning.TButton', 
                        background=cls.WARNING,
                        foreground=cls.TEXT_LIGHT)
        
        style.map('Warning.TButton',
                  background=[('active', '#e67e22'), 
                              ('disabled', cls.TEXT_MUTED)])
        
        # Danger button
        style.configure('Danger.TButton', 
                        background=cls.DANGER,
                        foreground=cls.TEXT_LIGHT)
        
        style.map('Danger.TButton',
                  background=[('active', '#c0392b'), 
                              ('disabled', cls.TEXT_MUTED)])
        
        # Link button (looks like a hyperlink)
        style.configure('Link.TButton', 
                        background=cls.BG_MAIN,
                        foreground=cls.PRIMARY,
                        borderwidth=0,
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL, 'underline'))
        
        style.map('Link.TButton',
                  foreground=[('active', cls.PRIMARY_DARK), 
                              ('disabled', cls.TEXT_MUTED)],
                  background=[('active', cls.BG_MAIN), 
                              ('disabled', cls.BG_MAIN)])
    
    @classmethod
    def _configure_entry_styles(cls, style):
        """Configure entry styles"""
        style.configure('TEntry',
                        fieldbackground=cls.LIGHT,
                        foreground=cls.TEXT_DARK,
                        bordercolor=cls.BORDER,
                        lightcolor=cls.BORDER,
                        darkcolor=cls.BORDER,
                        borderwidth=1,
                        padding=cls.PADDING_SMALL)
        
        style.map('TEntry',
                  fieldbackground=[('disabled', cls.BG_SECONDARY)],
                  foreground=[('disabled', cls.TEXT_MUTED)],
                  bordercolor=[('focus', cls.PRIMARY)])
    
    @classmethod
    def _configure_treeview_styles(cls, style):
        """Configure treeview styles"""
        style.configure('Treeview',
                        background=cls.LIGHT,
                        foreground=cls.TEXT_DARK,
                        rowheight=25,
                        fieldbackground=cls.LIGHT,
                        borderwidth=0,
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL))
        
        style.map('Treeview',
                  background=[('selected', cls.PRIMARY)],
                  foreground=[('selected', cls.TEXT_LIGHT)])
        
        style.configure('Treeview.Heading',
                        background=cls.DARK,
                        foreground=cls.TEXT_LIGHT,
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL, 'bold'),
                        relief='flat',
                        padding=cls.PADDING_SMALL)
        
        style.map('Treeview.Heading',
                  background=[('active', cls.DARKER)])
    
    @classmethod
    def _configure_notebook_styles(cls, style):
        """Configure notebook styles"""
        style.configure('TNotebook',
                        background=cls.BG_MAIN,
                        borderwidth=0)
        
        style.configure('TNotebook.Tab',
                        background=cls.BG_SECONDARY,
                        foreground=cls.TEXT_DARK,
                        padding=(cls.PADDING_MEDIUM, cls.PADDING_SMALL),
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL))
        
        style.map('TNotebook.Tab',
                  background=[('selected', cls.PRIMARY), ('active', cls.BG_HIGHLIGHT)],
                  foreground=[('selected', cls.TEXT_LIGHT), ('active', cls.TEXT_DARK)],
                  expand=[('selected', [1, 1, 1, 0])])
    
    @classmethod
    def _configure_frame_styles(cls, style):
        """Configure frame styles"""
        style.configure('TFrame',
                        background=cls.BG_MAIN)
        
        style.configure('Card.TFrame',
                        background=cls.LIGHT,
                        relief='solid',
                        borderwidth=1,
                        bordercolor=cls.BORDER)
        
        style.configure('Header.TFrame',
                        background=cls.PRIMARY,
                        relief='flat')
        
        style.configure('Footer.TFrame',
                        background=cls.DARK,
                        relief='flat')
    
    @classmethod
    def _configure_label_styles(cls, style):
        """Configure label styles"""
        style.configure('TLabel',
                        background=cls.BG_MAIN,
                        foreground=cls.TEXT_DARK,
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL))
        
        style.configure('Title.TLabel',
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_XLARGE, 'bold'),
                        foreground=cls.PRIMARY)
        
        style.configure('Subtitle.TLabel',
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_LARGE, 'bold'),
                        foreground=cls.TEXT_DARK)
        
        style.configure('Header.TLabel',
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_XXLARGE, 'bold'),
                        foreground=cls.PRIMARY)
        
        style.configure('WhiteTitle.TLabel',
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_XLARGE, 'bold'),
                        foreground=cls.TEXT_LIGHT,
                        background=cls.PRIMARY)
        
        style.configure('WhiteSubtitle.TLabel',
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_LARGE, 'bold'),
                        foreground=cls.TEXT_LIGHT,
                        background=cls.PRIMARY)
        
        style.configure('Info.TLabel',
                        foreground=cls.INFO)
        
        style.configure('Success.TLabel',
                        foreground=cls.SUCCESS)
        
        style.configure('Warning.TLabel',
                        foreground=cls.WARNING)
        
        style.configure('Error.TLabel',
                        foreground=cls.ERROR)
    
    @classmethod
    def _configure_combobox_styles(cls, style):
        """Configure combobox styles"""
        style.configure('TCombobox',
                        fieldbackground=cls.LIGHT,
                        background=cls.PRIMARY,
                        foreground=cls.TEXT_DARK,
                        arrowcolor=cls.PRIMARY,
                        bordercolor=cls.BORDER,
                        lightcolor=cls.BORDER,
                        darkcolor=cls.BORDER,
                        padding=cls.PADDING_SMALL)
        
        style.map('TCombobox',
                  fieldbackground=[('readonly', cls.LIGHT), ('disabled', cls.BG_SECONDARY)],
                  foreground=[('readonly', cls.TEXT_DARK), ('disabled', cls.TEXT_MUTED)],
                  selectbackground=[('readonly', cls.PRIMARY)],
                  selectforeground=[('readonly', cls.TEXT_LIGHT)])
    
    @classmethod
    def _configure_progressbar_styles(cls, style):
        """Configure progressbar styles"""
        style.configure('TProgressbar',
                        troughcolor=cls.BG_SECONDARY,
                        background=cls.PRIMARY,
                        borderwidth=0)
        
        style.configure('Success.TProgressbar',
                        background=cls.SUCCESS)
        
        style.configure('Warning.TProgressbar',
                        background=cls.WARNING)
        
        style.configure('Danger.TProgressbar',
                        background=cls.DANGER)
    
    @classmethod
    def _configure_scale_styles(cls, style):
        """Configure scale styles"""
        style.configure('TScale',
                        troughcolor=cls.BG_SECONDARY,
                        background=cls.PRIMARY,
                        borderwidth=0)
    
    @classmethod
    def _configure_scrollbar_styles(cls, style):
        """Configure scrollbar styles"""
        style.configure('TScrollbar',
                        troughcolor=cls.BG_SECONDARY,
                        background=cls.PRIMARY,
                        borderwidth=0,
                        arrowcolor=cls.TEXT_LIGHT)
        
        style.map('TScrollbar',
                  background=[('active', cls.PRIMARY_DARK), 
                              ('disabled', cls.TEXT_MUTED)])

# Custom widgets
class RoundedButton(tk.Canvas):
    """A custom rounded button widget"""
    def __init__(self, parent, text="Button", command=None, radius=10, 
                 bg_color=AppStyles.PRIMARY, fg_color=AppStyles.TEXT_LIGHT, 
                 hover_color=AppStyles.PRIMARY_DARK, width=120, height=40, 
                 font=(AppStyles.FONT_FAMILY, AppStyles.FONT_SIZE_NORMAL), **kwargs):
        super().__init__(parent, width=width, height=height, 
                         bg=parent["bg"], highlightthickness=0, **kwargs)
        
        self.radius = radius
        self.bg_color = bg_color
        self.fg_color = fg_color
        self.hover_color = hover_color
        self.current_color = bg_color
        self.command = command
        self.text = text
        self.font = font
        
        # Draw the initial button
        self._draw_button()
        
        # Bind events
        self.bind("<Enter>", self._on_enter)
        self.bind("<Leave>", self._on_leave)
        self.bind("<Button-1>", self._on_click)
        self.bind("<ButtonRelease-1>", self._on_release)
    
    def _draw_button(self):
        """Draw the button on the canvas"""
        self.delete("all")
        
        # Draw the rounded rectangle
        self.create_rounded_rect(0, 0, self.winfo_width(), self.winfo_height(), 
                                 self.radius, fill=self.current_color, outline="")
        
        # Draw the text
        self.create_text(self.winfo_width() / 2, self.winfo_height() / 2, 
                         text=self.text, fill=self.fg_color, font=self.font)
    
    def create_rounded_rect(self, x1, y1, x2, y2, radius, **kwargs):
        """Create a rounded rectangle"""
        points = [
            x1 + radius, y1,
            x2 - radius, y1,
            x2, y1,
            x2, y1 + radius,
            x2, y2 - radius,
            x2, y2,
            x2 - radius, y2,
            x1 + radius, y2,
            x1, y2,
            x1, y2 - radius,
            x1, y1 + radius,
            x1, y1
        ]
        
        return self.create_polygon(points, **kwargs, smooth=True)
    
    def _on_enter(self, event):
        """Handle mouse enter event"""
        self.current_color = self.hover_color
        self._draw_button()
    
    def _on_leave(self, event):
        """Handle mouse leave event"""
        self.current_color = self.bg_color
        self._draw_button()
    
    def _on_click(self, event):
        """Handle mouse click event"""
        self.current_color = self.bg_color
        self._draw_button()
    
    def _on_release(self, event):
        """Handle mouse release event"""
        self.current_color = self.hover_color
        self._draw_button()
        if self.command:
            self.command()
    
    def configure(self, **kwargs):
        """Configure the button properties"""
        if "text" in kwargs:
            self.text = kwargs.pop("text")
        if "bg_color" in kwargs:
            self.bg_color = kwargs.pop("bg_color")
            self.current_color = self.bg_color
        if "fg_color" in kwargs:
            self.fg_color = kwargs.pop("fg_color")
        if "hover_color" in kwargs:
            self.hover_color = kwargs.pop("hover_color")
        if "command" in kwargs:
            self.command = kwargs.pop("command")
        if "font" in kwargs:
            self.font = kwargs.pop("font")
        
        super().configure(**kwargs)
        self._draw_button()

class GradientFrame(tk.Canvas):
    """A frame with a gradient background"""
    def __init__(self, parent, color1=AppStyles.PRIMARY, color2=AppStyles.PRIMARY_DARK, 
                 direction="horizontal", **kwargs):
        super().__init__(parent, **kwargs)
        self.color1 = color1
        self.color2 = color2
        self.direction = direction
        
        self.bind("<Configure>", self._draw_gradient)
    
    def _draw_gradient(self, event=None):
        """Draw the gradient background"""
        self.delete("gradient")
        width = self.winfo_width()
        height = self.winfo_height()
        
        # Create gradient
        if self.direction == "horizontal":
            for i in range(width):
                # Calculate color for this line
                r1, g1, b1 = self.winfo_rgb(self.color1)
                r2, g2, b2 = self.winfo_rgb(self.color2)
                
                # Convert to 8-bit values
                r1, g1, b1 = r1 >> 8, g1 >> 8, b1 >> 8
                r2, g2, b2 = r2 >> 8, g2 >> 8, b2 >> 8
                
                # Calculate the color for this position
                ratio = i / width
                r = int(r1 * (1 - ratio) + r2 * ratio)
                g = int(g1 * (1 - ratio) + g2 * ratio)
                b = int(b1 * (1 - ratio) + b2 * ratio)
                
                color = f"#{r:02x}{g:02x}{b:02x}"
                self.create_line(i, 0, i, height, tags=("gradient",), fill=color)
        else:  # vertical
            for i in range(height):
                # Calculate color for this line
                r1, g1, b1 = self.winfo_rgb(self.color1)
                r2, g2, b2 = self.winfo_rgb(self.color2)
                
                # Convert to 8-bit values
                r1, g1, b1 = r1 >> 8, g1 >> 8, b1 >> 8
                r2, g2, b2 = r2 >> 8, g2 >> 8, b2 >> 8
                
                # Calculate the color for this position
                ratio = i / height
                r = int(r1 * (1 - ratio) + r2 * ratio)
                g = int(g1 * (1 - ratio) + g2 * ratio)
                b = int(b1 * (1 - ratio) + b2 * ratio)
                
                color = f"#{r:02x}{g:02x}{b:02x}"
                self.create_line(0, i, width, i, tags=("gradient",), fill=color)

class AnimatedProgressbar(ttk.Frame):
    """A progressbar with animation effects"""
    def __init__(self, parent, maximum=100, value=0, **kwargs):
        super().__init__(parent, **kwargs)
        
        self.maximum = maximum
        self.current_value = value
        self.target_value = value
        
        # Create the progressbar
        self.progressbar = ttk.Progressbar(self, orient="horizontal", 
                                          length=200, mode="determinate", 
                                          maximum=maximum, value=value)
        self.progressbar.pack(fill=tk.X, expand=True)
        
        # Create the value label
        self.value_label = ttk.Label(self, text=f"{value}%")
        self.value_label.pack(pady=(5, 0))
        
        # Animation variables
        self.animation_id = None
        self.animation_step = 1
    
    def set(self, value):
        """Set the progressbar value with animation"""
        if value == self.current_value:
            return
        
        self.target_value = value
        
        # Cancel any existing animation
        if self.animation_id:
            self.after_cancel(self.animation_id)
        
        # Start the animation
        self._animate()
    
    def _animate(self):
        """Animate the progressbar value change"""
        if self.current_value < self.target_value:
            self.current_value = min(self.current_value + self.animation_step, self.target_value)
        else:
            self.current_value = max(self.current_value - self.animation_step, self.target_value)
        
        # Update the progressbar and label
        self.progressbar["value"] = self.current_value
        self.value_label["text"] = f"{int(self.current_value)}%"
        
        # Continue the animation if needed
        if self.current_value != self.target_value:
            self.animation_id = self.after(10, self._animate)
        else:
            self.animation_id = None

class ToastNotification:
    """A toast notification that appears and disappears automatically"""
    def __init__(self, parent, message, type_="info", duration=3000):
        self.parent = parent
        self.message = message
        self.type = type_
        self.duration = duration
        
        # Create the toast window
        self.toast = tk.Toplevel(parent)
        self.toast.overrideredirect(True)
        self.toast.attributes("-topmost", True)
        
        # Set background color based on type
        if type_ == "info":
            bg_color = AppStyles.INFO
        elif type_ == "success":
            bg_color = AppStyles.SUCCESS
        elif type_ == "warning":
            bg_color = AppStyles.WARNING
        elif type_ == "error":
            bg_color = AppStyles.ERROR
        else:
            bg_color = AppStyles.DARK
        
        # Create the frame
        self.frame = tk.Frame(self.toast, bg=bg_color, padx=15, pady=10)
        self.frame.pack(fill=tk.BOTH, expand=True)
        
        # Create the message label
        self.label = tk.Label(self.frame, text=message, bg=bg_color, 
                             fg=AppStyles.TEXT_LIGHT, font=(AppStyles.FONT_FAMILY, 12))
        self.label.pack(padx=5, pady=5)
        
        # Position the toast at the bottom right of the parent
        self.toast.update_idletasks()
        parent_x = parent.winfo_rootx()
        parent_y = parent.winfo_rooty()
        parent_width = parent.winfo_width()
        parent_height = parent.winfo_height()
        
        toast_width = self.toast.winfo_width()
        toast_height = self.toast.winfo_height()
        
        x = parent_x + parent_width - toast_width - 20
        y = parent_y + parent_height - toast_height - 20
        
        self.toast.geometry(f"+{x}+{y}")
        
        # Schedule the toast to disappear
        self.parent.after(duration, self.destroy)
    
    def destroy(self):
        """Destroy the toast notification"""
        self.toast.destroy()

# Helper functions
def create_tooltip(widget, text):
    """Create a tooltip for a widget"""
    def enter(event):
        x, y, _, _ = widget.bbox("insert")
        x += widget.winfo_rootx() + 25
        y += widget.winfo_rooty() + 25
        
        # Create a toplevel window
        tooltip = tk.Toplevel(widget)
        tooltip.wm_overrideredirect(True)
        tooltip.wm_geometry(f"+{x}+{y}")
        
        # Create a label
        label = tk.Label(tooltip, text=text, justify=tk.LEFT,
                        background=AppStyles.DARK, foreground=AppStyles.TEXT_LIGHT,
                        relief=tk.SOLID, borderwidth=1,
                        font=(AppStyles.FONT_FAMILY, AppStyles.FONT_SIZE_SMALL))
        label.pack(padx=5, pady=5)
        
        widget.tooltip = tooltip
    
    def leave(event):
        if hasattr(widget, "tooltip"):
            widget.tooltip.destroy()
    
    widget.bind("<Enter>", enter)
    widget.bind("<Leave>", leave)

def load_image(path, width=None, height=None):
    """Load an image and optionally resize it"""
    try:
        from PIL import Image, ImageTk
        
        # Check if the file exists
        if not os.path.exists(path):
            return None
        
        # Open the image
        image = Image.open(path)
        
        # Resize if needed
        if width and height:
            image = image.resize((width, height), Image.LANCZOS)
        
        # Convert to PhotoImage
        photo = ImageTk.PhotoImage(image)
        return photo
    except Exception as e:
        print(f"Error loading image: {e}")
        return None

def center_window(window, width=None, height=None):
    """Center a window on the screen"""
    if width and height:
        window.update_idletasks()
        
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        window.geometry(f"{width}x{height}+{x}+{y}")
    else:
        window.update_idletasks()
        
        width = window.winfo_width()
        height = window.winfo_height()
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        window.geometry(f"+{x}+{y}")

def create_rounded_frame(parent, bg_color=AppStyles.LIGHT, corner_radius=10, **kwargs):
    """Create a frame with rounded corners"""
    frame = tk.Frame(parent, bg=parent["bg"], highlightthickness=0, **kwargs)
    
    def _update_canvas(event):
        canvas.delete("all")
        canvas.create_rounded_rectangle(0, 0, event.width, event.height, 
                                       radius=corner_radius, fill=bg_color)
    
    canvas = tk.Canvas(frame, bg=parent["bg"], highlightthickness=0)
    canvas.pack(fill=tk.BOTH, expand=True)
    
    # Create a rounded rectangle on the canvas
    canvas.create_rounded_rectangle = lambda x1, y1, x2, y2, radius, **kwargs: \
        canvas.create_polygon(
            x1 + radius, y1,
            x2 - radius, y1,
            x2, y1,
            x2, y1 + radius,
            x2, y2 - radius,
            x2, y2,
            x2 - radius, y2,
            x1 + radius, y2,
            x1, y2,
            x1, y2 - radius,
            x1, y1 + radius,
            x1, y1,
            smooth=True, **kwargs
        )
    
    canvas.bind("<Configure>", _update_canvas)
    
    # Create an inner frame for content
    inner_frame = tk.Frame(canvas, bg=bg_color, highlightthickness=0)
    canvas.create_window(0, 0, anchor="nw", window=inner_frame, 
                        width=frame.winfo_reqwidth(), 
                        height=frame.winfo_reqheight())
    
    # Update the inner frame size when the canvas changes
    def _update_inner_frame(event):
        canvas.itemconfig("inner_window", width=event.width, height=event.height)
    
    canvas.bind("<Configure>", lambda event: (_update_canvas(event), _update_inner_frame(event)))
    
    return frame, inner_frame

# Export the styles
__all__ = ['AppStyles', 'RoundedButton', 'GradientFrame', 'AnimatedProgressbar', 
           'ToastNotification', 'create_tooltip', 'load_image', 'center_window',
           'create_rounded_frame']