import sqlite3
import datetime

class Customer:
    def __init__(self, db_connection):
        """Initialize Customer model with database connection"""
        self.conn = db_connection
        self.cursor = self.conn.cursor()
    
    def get_all_customers(self):
        """Get all customers from the database"""
        try:
            self.cursor.execute("SELECT * FROM customers")
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error getting customers: {e}")
            return []
    
    def get_customer_by_id(self, customer_id):
        """Get a customer by ID"""
        try:
            self.cursor.execute("SELECT * FROM customers WHERE id = ?", (customer_id,))
            return self.cursor.fetchone()
        except sqlite3.Error as e:
            print(f"Error getting customer by ID: {e}")
            return None
    
    def search_customers(self, search_term):
        """Search customers by name, phone, or email"""
        try:
            search_pattern = f"%{search_term}%"
            self.cursor.execute('''
            SELECT * FROM customers 
            WHERE name LIKE ? OR phone LIKE ? OR email LIKE ?
            ''', (search_pattern, search_pattern, search_pattern))
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error searching customers: {e}")
            return []
    
    def add_customer(self, name, phone=None, email=None, address=None, loyalty_points=0):
        """Add a new customer to the database"""
        try:
            self.cursor.execute('''
            INSERT INTO customers (name, phone, email, address, loyalty_points)
            VALUES (?, ?, ?, ?, ?)
            ''', (name, phone, email, address, loyalty_points))
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"Error adding customer: {e}")
            return None
    
    def update_customer(self, customer_id, name=None, phone=None, email=None, address=None):
        """Update an existing customer"""
        try:
            # Get current customer data
            self.cursor.execute("SELECT * FROM customers WHERE id = ?", (customer_id,))
            customer = self.cursor.fetchone()
            if not customer:
                return False
            
            # Use current values if new ones not provided
            name = name if name is not None else customer[1]
            phone = phone if phone is not None else customer[2]
            email = email if email is not None else customer[3]
            address = address if address is not None else customer[4]
            
            self.cursor.execute('''
            UPDATE customers 
            SET name = ?, phone = ?, email = ?, address = ?
            WHERE id = ?
            ''', (name, phone, email, address, customer_id))
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error updating customer: {e}")
            return False
    
    def update_loyalty_points(self, customer_id, points_change):
        """Update customer loyalty points"""
        try:
            # Get current points
            self.cursor.execute("SELECT loyalty_points FROM customers WHERE id = ?", (customer_id,))
            result = self.cursor.fetchone()
            if not result:
                return False
            
            current_points = result[0]
            new_points = current_points + points_change
            
            # Update points
            self.cursor.execute('''
            UPDATE customers 
            SET loyalty_points = ?
            WHERE id = ?
            ''', (new_points, customer_id))
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error updating loyalty points: {e}")
            return False
    
    def update_last_purchase(self, customer_id):
        """Update customer's last purchase timestamp"""
        try:
            self.cursor.execute('''
            UPDATE customers 
            SET last_purchase = ?
            WHERE id = ?
            ''', (datetime.datetime.now(), customer_id))
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error updating last purchase: {e}")
            return False
    
    def delete_customer(self, customer_id):
        """Delete a customer from the database"""
        try:
            self.cursor.execute("DELETE FROM customers WHERE id = ?", (customer_id,))
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error deleting customer: {e}")
            return False
    
    def get_top_customers(self, limit=10):
        """Get top customers by loyalty points"""
        try:
            self.cursor.execute('''
            SELECT c.*, COUNT(s.id) as purchase_count, SUM(s.total_amount) as total_spent
            FROM customers c
            LEFT JOIN sales s ON c.id = s.customer_id
            GROUP BY c.id
            ORDER BY c.loyalty_points DESC, total_spent DESC
            LIMIT ?
            ''', (limit,))
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error getting top customers: {e}")
            return []