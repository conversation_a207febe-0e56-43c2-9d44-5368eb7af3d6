import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import os
import sys
from PIL import Image, ImageTk
import datetime

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from models.user import User
from database.db_setup import DatabaseSetup
from ui.styles import AppSty<PERSON>, RoundedButton, GradientFrame, create_tooltip, load_image, center_window

class LoginWindow:
    def __init__(self, root, db_connection, on_login_success=None):
        """Initialize the login window"""
        self.root = root
        self.conn = db_connection
        self.on_login_success = on_login_success
        self.user_model = User(self.conn)
        
        # Configure the window
        self.root.title("POSMADORA - تسجيل الدخول")
        self.root.geometry("900x600")
        self.root.resizable(False, False)
        
        # Set window icon if available
        try:
            self.root.iconbitmap("resources/images/icon.ico")
        except:
            pass
        
        # Apply styles
        self.style = AppStyles.setup_styles(self.root)
        
        # Create main container
        self.main_container = tk.Frame(self.root, bg=AppStyles.BG_MAIN)
        self.main_container.pack(fill=tk.BOTH, expand=True)
        
        # Create left panel with gradient background
        self.left_panel = GradientFrame(self.main_container, 
                                       color1=AppStyles.PRIMARY, 
                                       color2=AppStyles.PRIMARY_DARK,
                                       direction="vertical",
                                       highlightthickness=0)
        self.left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Create right panel for login form
        self.right_panel = tk.Frame(self.main_container, bg=AppStyles.BG_MAIN)
        self.right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # Add content to left panel
        self.setup_branding_panel()
        
        # Add content to right panel
        self.setup_login_form()
        
        # Center the window on screen
        center_window(self.root, 900, 600)
        
        # Set initial focus
        self.username_entry.focus()
        
        # Start clock update
        self.update_datetime()
    
    def setup_branding_panel(self):
        """Set up the branding panel on the left side"""
        # Create a frame for branding content
        branding_frame = tk.Frame(self.left_panel, bg=AppStyles.PRIMARY)
        branding_frame.place(relx=0.5, rely=0.5, anchor=tk.CENTER)
        
        # App logo/name
        logo_label = tk.Label(branding_frame, text="POSMADORA", 
                             font=(AppStyles.FONT_FAMILY, 36, 'bold'),
                             fg=AppStyles.TEXT_LIGHT, bg=AppStyles.PRIMARY)
        logo_label.pack(pady=(0, 10))
        
        # Tagline
        tagline_label = tk.Label(branding_frame, text="نظام نقاط البيع المتكامل",
                                font=(AppStyles.FONT_FAMILY_ARABIC, 18),
                                fg=AppStyles.TEXT_LIGHT, bg=AppStyles.PRIMARY)
        tagline_label.pack(pady=(0, 30))
        
        # Try to load and display logo image
        try:
            logo_path = "resources/images/logo.png"
            logo_image = load_image(logo_path, 200, 200)
            if logo_image:
                image_label = tk.Label(branding_frame, image=logo_image, 
                                      bg=AppStyles.PRIMARY, bd=0)
                image_label.image = logo_image  # Keep a reference
                image_label.pack(pady=20)
            else:
                # If logo image not found, show a placeholder
                placeholder = tk.Label(branding_frame, text="[Logo]", 
                                      font=(AppStyles.FONT_FAMILY, 24),
                                      fg=AppStyles.TEXT_LIGHT, bg=AppStyles.PRIMARY,
                                      width=8, height=4, bd=1, relief=tk.SOLID)
                placeholder.pack(pady=20)
        except Exception as e:
            print(f"Error loading logo: {e}")
            # If logo image not found, show a placeholder
            placeholder = tk.Label(branding_frame, text="[Logo]", 
                                  font=(AppStyles.FONT_FAMILY, 24),
                                  fg=AppStyles.TEXT_LIGHT, bg=AppStyles.PRIMARY,
                                  width=8, height=4, bd=1, relief=tk.SOLID)
            placeholder.pack(pady=20)
        
        # Features list
        features_frame = tk.Frame(branding_frame, bg=AppStyles.PRIMARY)
        features_frame.pack(pady=20)
        
        features = [
            "✓ إدارة المبيعات والفواتير",
            "✓ إدارة المخزون والمنتجات",
            "✓ إدارة العملاء وبرامج الولاء",
            "✓ تقارير مفصلة وإحصائيات"
        ]
        
        for feature in features:
            feature_label = tk.Label(features_frame, text=feature,
                                    font=(AppStyles.FONT_FAMILY_ARABIC, 12),
                                    fg=AppStyles.TEXT_LIGHT, bg=AppStyles.PRIMARY,
                                    anchor=tk.W, justify=tk.LEFT)
            feature_label.pack(pady=5, anchor=tk.W)
    
    def setup_login_form(self):
        """Set up the login form on the right side"""
        # Create a frame for the login form
        login_frame = tk.Frame(self.right_panel, bg=AppStyles.BG_MAIN)
        login_frame.place(relx=0.5, rely=0.5, anchor=tk.CENTER, width=350)
        
        # Date and time display
        self.datetime_frame = tk.Frame(self.right_panel, bg=AppStyles.BG_MAIN)
        self.datetime_frame.place(x=20, y=20)
        
        self.date_var = tk.StringVar()
        self.time_var = tk.StringVar()
        
        date_label = tk.Label(self.datetime_frame, textvariable=self.date_var,
                             font=(AppStyles.FONT_FAMILY, 10),
                             fg=AppStyles.TEXT_MUTED, bg=AppStyles.BG_MAIN)
        date_label.pack(anchor=tk.W)
        
        time_label = tk.Label(self.datetime_frame, textvariable=self.time_var,
                             font=(AppStyles.FONT_FAMILY, 10, 'bold'),
                             fg=AppStyles.TEXT_DARK, bg=AppStyles.BG_MAIN)
        time_label.pack(anchor=tk.W)
        
        # Login header
        header_label = tk.Label(login_frame, text="تسجيل الدخول",
                               font=(AppStyles.FONT_FAMILY_ARABIC, 24, 'bold'),
                               fg=AppStyles.PRIMARY, bg=AppStyles.BG_MAIN)
        header_label.pack(pady=(0, 30))
        
        # Username field
        username_label = tk.Label(login_frame, text="اسم المستخدم",
                                 font=(AppStyles.FONT_FAMILY_ARABIC, 12),
                                 fg=AppStyles.TEXT_DARK, bg=AppStyles.BG_MAIN,
                                 anchor=tk.W)
        username_label.pack(fill=tk.X, pady=(0, 5))
        
        self.username_var = tk.StringVar()
        self.username_entry = ttk.Entry(login_frame, textvariable=self.username_var,
                                       font=(AppStyles.FONT_FAMILY, 12),
                                       width=30, style='TEntry')
        self.username_entry.pack(fill=tk.X, pady=(0, 15))
        
        # Password field
        password_label = tk.Label(login_frame, text="كلمة المرور",
                                 font=(AppStyles.FONT_FAMILY_ARABIC, 12),
                                 fg=AppStyles.TEXT_DARK, bg=AppStyles.BG_MAIN,
                                 anchor=tk.W)
        password_label.pack(fill=tk.X, pady=(0, 5))
        
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(login_frame, textvariable=self.password_var,
                                       font=(AppStyles.FONT_FAMILY, 12),
                                       width=30, style='TEntry', show="•")
        self.password_entry.pack(fill=tk.X, pady=(0, 15))
        
        # Show/hide password
        self.show_password_var = tk.BooleanVar(value=False)
        show_password_check = ttk.Checkbutton(login_frame, text="إظهار كلمة المرور",
                                             variable=self.show_password_var,
                                             command=self.toggle_password_visibility)
        show_password_check.pack(anchor=tk.W, pady=(0, 15))
        
        # Shift selection
        shift_label = tk.Label(login_frame, text="الوردية",
                              font=(AppStyles.FONT_FAMILY_ARABIC, 12),
                              fg=AppStyles.TEXT_DARK, bg=AppStyles.BG_MAIN,
                              anchor=tk.W)
        shift_label.pack(fill=tk.X, pady=(0, 5))
        
        shift_frame = tk.Frame(login_frame, bg=AppStyles.BG_MAIN)
        shift_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.shift_var = tk.StringVar(value="day")
        
        day_radio = ttk.Radiobutton(shift_frame, text="نهاري",
                                   variable=self.shift_var, value="day")
        day_radio.pack(side=tk.LEFT, padx=(0, 20))
        
        night_radio = ttk.Radiobutton(shift_frame, text="ليلي",
                                     variable=self.shift_var, value="night")
        night_radio.pack(side=tk.LEFT)
        
        # Login button
        self.login_button = RoundedButton(login_frame, text="تسجيل الدخول",
                                         command=self.login,
                                         bg_color=AppStyles.PRIMARY,
                                         fg_color=AppStyles.TEXT_LIGHT,
                                         hover_color=AppStyles.PRIMARY_DARK,
                                         width=350, height=40,
                                         font=(AppStyles.FONT_FAMILY_ARABIC, 14, 'bold'))
        self.login_button.pack(pady=20)
        
        # Status message
        self.status_var = tk.StringVar()
        self.status_label = tk.Label(login_frame, textvariable=self.status_var,
                                    font=(AppStyles.FONT_FAMILY_ARABIC, 10),
                                    fg=AppStyles.DANGER, bg=AppStyles.BG_MAIN)
        self.status_label.pack(pady=10)
        
        # Version info
        version_label = tk.Label(self.right_panel, text="POSMADORA v1.0",
                                font=(AppStyles.FONT_FAMILY, 8),
                                fg=AppStyles.TEXT_MUTED, bg=AppStyles.BG_MAIN)
        version_label.place(relx=1.0, rely=1.0, x=-10, y=-10, anchor=tk.SE)
        
        # Bind Enter key to login
        self.username_entry.bind("<Return>", lambda event: self.password_entry.focus())
        self.password_entry.bind("<Return>", lambda event: self.login())
    
    def toggle_password_visibility(self):
        """Toggle password visibility"""
        if self.show_password_var.get():
            self.password_entry.config(show="")
        else:
            self.password_entry.config(show="•")
    
    def update_datetime(self):
        """Update the date and time display"""
        now = datetime.datetime.now()
        
        # Update date and time variables
        self.date_var.set(now.strftime("%A, %d %B %Y"))
        self.time_var.set(now.strftime("%H:%M:%S"))
        
        # Schedule the next update
        self.root.after(1000, self.update_datetime)
    
    def login(self):
        """Authenticate user and login"""
        # Disable login button to prevent multiple clicks
        self.login_button.configure(state=tk.DISABLED)
        
        # Get input values
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()
        shift = self.shift_var.get()
        
        # Validate input
        if not username or not password:
            self.status_var.set("الرجاء إدخال اسم المستخدم وكلمة المرور")
            self.login_button.configure(state=tk.NORMAL)
            return
        
        # Show loading state
        self.status_var.set("جاري التحقق...")
        self.root.update()
        
        # Authenticate user (with a small delay to show loading state)
        self.root.after(500)  # Simulate network delay
        user = self.user_model.authenticate_user(username, password)
        
        if user:
            # Check if user is assigned to the selected shift
            if user[5] != shift and user[4] != 'admin':  # Check shift and role
                self.status_var.set(f"غير مصرح لك بالعمل في الوردية {shift}")
                self.login_button.configure(state=tk.NORMAL)
                return
            
            # Login successful
            self.status_var.set("تم تسجيل الدخول بنجاح!")
            self.status_label.configure(fg=AppStyles.SUCCESS)
            
            # Update last login time
            try:
                self.user_model.update_last_login(user[0])
            except Exception as e:
                print(f"Error updating last login: {e}")
            
            # Show welcome message
            self.root.after(500, lambda: self.show_welcome_message(user))
        else:
            self.status_var.set("اسم المستخدم أو كلمة المرور غير صحيحة")
            self.status_label.configure(fg=AppStyles.DANGER)
            self.password_var.set("")  # Clear password field
            self.login_button.configure(state=tk.NORMAL)
    
    def show_welcome_message(self, user):
        """Show welcome message and proceed to main application"""
        # Create a welcome message
        welcome_text = f"مرحباً {user[3]}!"
        role_text = f"الدور: {user[4]}"
        shift_text = f"الوردية: {'نهاري' if user[5] == 'day' else 'ليلي'}"
        
        # Create a toplevel window for the welcome message
        welcome_window = tk.Toplevel(self.root)
        welcome_window.title("مرحباً")
        welcome_window.geometry("300x200")
        welcome_window.resizable(False, False)
        welcome_window.configure(bg=AppStyles.BG_MAIN)
        welcome_window.transient(self.root)
        welcome_window.grab_set()
        
        # Center the window
        center_window(welcome_window, 300, 200)
        
        # Add content
        welcome_frame = tk.Frame(welcome_window, bg=AppStyles.BG_MAIN, padx=20, pady=20)
        welcome_frame.pack(fill=tk.BOTH, expand=True)
        
        # Welcome message
        tk.Label(welcome_frame, text=welcome_text,
                font=(AppStyles.FONT_FAMILY_ARABIC, 16, 'bold'),
                fg=AppStyles.PRIMARY, bg=AppStyles.BG_MAIN).pack(pady=(0, 10))
        
        # User info
        tk.Label(welcome_frame, text=role_text,
                font=(AppStyles.FONT_FAMILY_ARABIC, 12),
                fg=AppStyles.TEXT_DARK, bg=AppStyles.BG_MAIN).pack(pady=2)
        
        tk.Label(welcome_frame, text=shift_text,
                font=(AppStyles.FONT_FAMILY_ARABIC, 12),
                fg=AppStyles.TEXT_DARK, bg=AppStyles.BG_MAIN).pack(pady=2)
        
        # Current date and time
        now = datetime.datetime.now()
        date_time_text = now.strftime("%Y-%m-%d %H:%M:%S")
        
        tk.Label(welcome_frame, text=f"تاريخ ووقت تسجيل الدخول:\n{date_time_text}",
                font=(AppStyles.FONT_FAMILY_ARABIC, 10),
                fg=AppStyles.TEXT_MUTED, bg=AppStyles.BG_MAIN).pack(pady=(10, 0))
        
        # Auto-close after 2 seconds and proceed to main application
        welcome_window.after(2000, lambda: self.proceed_to_main(welcome_window, user))
    
    def proceed_to_main(self, welcome_window, user):
        """Close welcome window and proceed to main application"""
        welcome_window.destroy()
        
        # Call the success callback if provided
        if self.on_login_success:
            self.on_login_success(user)


# For testing the login window independently
if __name__ == "__main__":
    # Setup database
    db = DatabaseSetup()
    db.connect()
    db.setup_tables()
    db.insert_sample_data()
    
    # Create root window
    root = tk.Tk()
    
    # Configure the root window
    root.configure(bg=AppStyles.BG_MAIN)
    
    # Define login success callback
    def on_login(user):
        print(f"User logged in: {user[3]} (ID: {user[0]}, Role: {user[4]}, Shift: {user[5]})")
        root.after(1000, root.destroy)  # Close after 1 second
    
    # Create login window
    login_window = LoginWindow(root, db.conn, on_login)
    
    # Start the main loop
    root.mainloop()
    
    # Close database connection
    db.close()