import sqlite3
import os
import datetime

class DatabaseSetup:
    def __init__(self, db_path='pos_database.db'):
        """Initialize database connection and setup tables if they don't exist"""
        self.db_path = db_path
        self.conn = None
        self.cursor = None
        
    def connect(self):
        """Connect to the database"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.cursor = self.conn.cursor()
            print(f"Connected to database: {self.db_path}")
            return True
        except sqlite3.Error as e:
            print(f"Database connection error: {e}")
            return False
    
    def close(self):
        """Close the database connection"""
        if self.conn:
            self.conn.close()
            print("Database connection closed")
    
    def setup_tables(self):
        """Create all necessary tables if they don't exist"""
        if not self.conn:
            if not self.connect():
                return False
        
        try:
            # Create Users/Staff table
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT NOT NULL,
                shift TEXT NOT NULL,
                contact TEXT,
                email TEXT,
                date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                active INTEGER DEFAULT 1
            )
            ''')
            
            # Create Products table
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                barcode TEXT UNIQUE,
                name TEXT NOT NULL,
                description TEXT,
                category TEXT,
                purchase_price REAL NOT NULL,
                selling_price REAL NOT NULL,
                quantity INTEGER DEFAULT 0,
                min_quantity INTEGER DEFAULT 5,
                image_path TEXT,
                supplier_id INTEGER,
                date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_updated TIMESTAMP,
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
            )
            ''')
            
            # Create Categories table
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            # Create Suppliers table
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                contact_person TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            # Create Customers table
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                email TEXT,
                address TEXT,
                loyalty_points INTEGER DEFAULT 0,
                date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_purchase TIMESTAMP
            )
            ''')
            
            # Create Sales table
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS sales (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                customer_id INTEGER,
                user_id INTEGER NOT NULL,
                total_amount REAL NOT NULL,
                discount_amount REAL DEFAULT 0,
                tax_amount REAL DEFAULT 0,
                payment_method TEXT NOT NULL,
                payment_status TEXT NOT NULL,
                sale_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
            ''')
            
            # Create Sale Items table
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS sale_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sale_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL,
                unit_price REAL NOT NULL,
                discount REAL DEFAULT 0,
                total_price REAL NOT NULL,
                FOREIGN KEY (sale_id) REFERENCES sales (id),
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
            ''')
            
            # Create Inventory Transactions table
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory_transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                transaction_type TEXT NOT NULL,
                quantity INTEGER NOT NULL,
                user_id INTEGER NOT NULL,
                notes TEXT,
                transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
            ''')
            
            # Create Settings table
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key TEXT UNIQUE NOT NULL,
                setting_value TEXT,
                description TEXT,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            # Commit the changes
            self.conn.commit()
            print("All tables created successfully")
            
            # Insert default admin user if not exists
            self.cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'admin'")
            if self.cursor.fetchone()[0] == 0:
                self.cursor.execute('''
                INSERT INTO users (username, password, full_name, role, shift)
                VALUES (?, ?, ?, ?, ?)
                ''', ('admin', 'admin123', 'System Administrator', 'admin', 'day'))
                self.conn.commit()
                print("Default admin user created")
            
            # Insert default settings
            default_settings = [
                ('store_name', 'POSMADORA', 'Store name displayed on receipts and UI'),
                ('store_address', 'Your Address Here', 'Store address displayed on receipts'),
                ('store_phone', 'Your Phone Number', 'Store contact number'),
                ('tax_rate', '15', 'Default tax rate percentage'),
                ('receipt_footer', 'Thank you for shopping with us!', 'Message at the bottom of receipts'),
                ('currency_symbol', '$', 'Currency symbol used in the application'),
                ('theme', 'light', 'UI theme (light/dark)'),
                ('backup_path', './backups', 'Path for database backups')
            ]
            
            for setting in default_settings:
                self.cursor.execute("SELECT COUNT(*) FROM settings WHERE setting_key = ?", (setting[0],))
                if self.cursor.fetchone()[0] == 0:
                    self.cursor.execute('''
                    INSERT INTO settings (setting_key, setting_value, description)
                    VALUES (?, ?, ?)
                    ''', setting)
            
            self.conn.commit()
            print("Default settings created")
            
            return True
            
        except sqlite3.Error as e:
            print(f"Error creating tables: {e}")
            return False

    def insert_sample_data(self):
        """Insert sample data for testing purposes"""
        if not self.conn:
            if not self.connect():
                return False
        
        try:
            # Insert sample categories
            categories = [
                ('Beverages', 'Drinks and liquids'),
                ('Bakery', 'Bread and baked goods'),
                ('Dairy', 'Milk and dairy products'),
                ('Produce', 'Fruits and vegetables'),
                ('Electronics', 'Electronic devices and accessories')
            ]
            
            for category in categories:
                self.cursor.execute("SELECT COUNT(*) FROM categories WHERE name = ?", (category[0],))
                if self.cursor.fetchone()[0] == 0:
                    self.cursor.execute('''
                    INSERT INTO categories (name, description)
                    VALUES (?, ?)
                    ''', category)
            
            # Insert sample suppliers
            suppliers = [
                ('Global Foods', 'John Smith', '123-456-7890', '<EMAIL>', '123 Main St'),
                ('Tech Supplies', 'Jane Doe', '************', '<EMAIL>', '456 Tech Blvd'),
                ('Fresh Farms', 'Robert Johnson', '************', '<EMAIL>', '789 Farm Rd')
            ]
            
            for supplier in suppliers:
                self.cursor.execute("SELECT COUNT(*) FROM suppliers WHERE name = ?", (supplier[0],))
                if self.cursor.fetchone()[0] == 0:
                    self.cursor.execute('''
                    INSERT INTO suppliers (name, contact_person, phone, email, address)
                    VALUES (?, ?, ?, ?, ?)
                    ''', supplier)
            
            # Get supplier IDs
            self.cursor.execute("SELECT id FROM suppliers WHERE name = 'Global Foods'")
            global_foods_id = self.cursor.fetchone()[0]
            
            self.cursor.execute("SELECT id FROM suppliers WHERE name = 'Tech Supplies'")
            tech_supplies_id = self.cursor.fetchone()[0]
            
            self.cursor.execute("SELECT id FROM suppliers WHERE name = 'Fresh Farms'")
            fresh_farms_id = self.cursor.fetchone()[0]
            
            # Insert sample products
            products = [
                ('8901234567890', 'Mineral Water', '500ml bottled water', 'Beverages', 0.50, 1.00, 100, 20, None, global_foods_id),
                ('7890123456789', 'Whole Wheat Bread', 'Fresh baked bread', 'Bakery', 1.20, 2.50, 30, 10, None, global_foods_id),
                ('6789012345678', 'Milk', '1L fresh milk', 'Dairy', 0.80, 1.50, 40, 15, None, fresh_farms_id),
                ('5678901234567', 'Apples', 'Fresh red apples (kg)', 'Produce', 1.50, 3.00, 50, 10, None, fresh_farms_id),
                ('4567890123456', 'USB Cable', '1m USB charging cable', 'Electronics', 2.00, 5.00, 25, 5, None, tech_supplies_id)
            ]
            
            for product in products:
                self.cursor.execute("SELECT COUNT(*) FROM products WHERE barcode = ?", (product[0],))
                if self.cursor.fetchone()[0] == 0:
                    self.cursor.execute('''
                    INSERT INTO products (barcode, name, description, category, purchase_price, selling_price, quantity, min_quantity, image_path, supplier_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', product)
            
            # Insert sample customers
            customers = [
                ('Walk-in Customer', '', '', '', 0),
                ('Ahmed Mohamed', '************', '<EMAIL>', 'Cairo, Egypt', 100),
                ('Fatima Ali', '************', '<EMAIL>', 'Alexandria, Egypt', 50)
            ]
            
            for customer in customers:
                self.cursor.execute("SELECT COUNT(*) FROM customers WHERE name = ?", (customer[0],))
                if self.cursor.fetchone()[0] == 0:
                    self.cursor.execute('''
                    INSERT INTO customers (name, phone, email, address, loyalty_points)
                    VALUES (?, ?, ?, ?, ?)
                    ''', customer)
            
            # Commit all changes
            self.conn.commit()
            print("Sample data inserted successfully")
            return True
            
        except sqlite3.Error as e:
            print(f"Error inserting sample data: {e}")
            return False

# If this file is run directly, set up the database
if __name__ == "__main__":
    db = DatabaseSetup()
    db.connect()
    db.setup_tables()
    db.insert_sample_data()
    db.close()