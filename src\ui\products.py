import tkinter as tk
from tkinter import ttk, messagebox, simpledialog, filedialog
import sqlite3
import os
import sys
from PIL import Image, ImageTk

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from models.product import Product

class ProductsWindow:
    def __init__(self, root, db_connection, current_user):
        """Initialize the products management window"""
        self.root = root
        self.conn = db_connection
        self.current_user = current_user
        
        # Initialize models
        self.product_model = Product(self.conn)
        
        # Configure the window
        self.root.title(f"POSMADORA - إدارة المنتجات - {current_user[3]}")
        self.root.geometry("1200x700")
        
        # Set window icon if available
        try:
            self.root.iconbitmap("resources/images/icon.ico")
        except:
            pass
        
        # Create a style
        self.style = ttk.Style()
        self.style.configure('TLabel', font=('Arial', 12))
        self.style.configure('TButton', font=('Arial', 12))
        self.style.configure('Header.TLabel', font=('Arial', 18, 'bold'))
        self.style.configure('Title.TLabel', font=('Arial', 14, 'bold'))
        
        # Create main container
        self.main_container = ttk.Frame(self.root, padding=10)
        self.main_container.pack(fill=tk.BOTH, expand=True)
        
        # Create header
        self.header_label = ttk.Label(self.main_container, text="إدارة المنتجات", style='Header.TLabel')
        self.header_label.pack(pady=(0, 20))
        
        # Create top frame for search and filters
        self.top_frame = ttk.Frame(self.main_container)
        self.top_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Search bar
        ttk.Label(self.top_frame, text="بحث:").pack(side=tk.LEFT, padx=5)
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(self.top_frame, textvariable=self.search_var, width=30)
        self.search_entry.pack(side=tk.LEFT, padx=5)
        
        self.search_btn = ttk.Button(self.top_frame, text="بحث", command=self.search_products)
        self.search_btn.pack(side=tk.LEFT, padx=5)
        
        # Category filter
        ttk.Label(self.top_frame, text="التصنيف:").pack(side=tk.LEFT, padx=(20, 5))
        self.category_var = tk.StringVar(value="الكل")
        self.categories = ["الكل"] + [cat[1] for cat in self.product_model.get_all_categories()]
        self.category_combo = ttk.Combobox(self.top_frame, textvariable=self.category_var, values=self.categories, width=15)
        self.category_combo.pack(side=tk.LEFT, padx=5)
        self.category_combo.bind("<<ComboboxSelected>>", self.filter_by_category)
        
        # Low stock filter
        self.low_stock_var = tk.BooleanVar()
        self.low_stock_check = ttk.Checkbutton(
            self.top_frame, text="المنتجات منخفضة المخزون فقط", 
            variable=self.low_stock_var, command=self.filter_products
        )
        self.low_stock_check.pack(side=tk.LEFT, padx=20)
        
        # Create main content with products list and details
        self.content_frame = ttk.Frame(self.main_container)
        self.content_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # Create products list on the left
        self.products_frame = ttk.LabelFrame(self.content_frame, text="قائمة المنتجات")
        self.products_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # Create product details on the right
        self.details_frame = ttk.LabelFrame(self.content_frame, text="تفاصيل المنتج")
        self.details_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0), pady=5)
        
        # Create products treeview
        self.create_products_treeview()
        
        # Create product details form
        self.create_product_details()
        
        # Create bottom buttons
        self.button_frame = ttk.Frame(self.main_container)
        self.button_frame.pack(fill=tk.X, pady=10)
        
        self.add_btn = ttk.Button(self.button_frame, text="إضافة منتج جديد", command=self.add_product)
        self.add_btn.pack(side=tk.LEFT, padx=5)
        
        self.edit_btn = ttk.Button(self.button_frame, text="تعديل المنتج", command=self.edit_product)
        self.edit_btn.pack(side=tk.LEFT, padx=5)
        
        self.delete_btn = ttk.Button(self.button_frame, text="حذف المنتج", command=self.delete_product)
        self.delete_btn.pack(side=tk.LEFT, padx=5)
        
        self.refresh_btn = ttk.Button(self.button_frame, text="تحديث القائمة", command=self.refresh_products)
        self.refresh_btn.pack(side=tk.LEFT, padx=5)
        
        self.close_btn = ttk.Button(self.button_frame, text="إغلاق", command=self.root.destroy)
        self.close_btn.pack(side=tk.RIGHT, padx=5)
        
        # Load products
        self.load_products()
    
    def create_products_treeview(self):
        """Create the products treeview"""
        # Create frame for treeview with scrollbar
        tree_frame = ttk.Frame(self.products_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create treeview
        columns = ('id', 'barcode', 'name', 'category', 'price', 'quantity', 'status')
        self.products_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=20)
        
        # Define headings
        self.products_tree.heading('id', text='#')
        self.products_tree.heading('barcode', text='الباركود')
        self.products_tree.heading('name', text='اسم المنتج')
        self.products_tree.heading('category', text='التصنيف')
        self.products_tree.heading('price', text='السعر')
        self.products_tree.heading('quantity', text='الكمية')
        self.products_tree.heading('status', text='الحالة')
        
        # Define columns
        self.products_tree.column('id', width=50, anchor=tk.CENTER)
        self.products_tree.column('barcode', width=120)
        self.products_tree.column('name', width=200)
        self.products_tree.column('category', width=100)
        self.products_tree.column('price', width=80, anchor=tk.E)
        self.products_tree.column('quantity', width=80, anchor=tk.CENTER)
        self.products_tree.column('status', width=100)
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.products_tree.yview)
        self.products_tree.configure(yscroll=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.products_tree.pack(fill=tk.BOTH, expand=True)
        
        # Bind selection event
        self.products_tree.bind('<<TreeviewSelect>>', self.on_product_select)
    
    def create_product_details(self):
        """Create the product details form"""
        # Create a canvas with scrollbar for the details
        self.details_canvas = tk.Canvas(self.details_frame)
        scrollbar = ttk.Scrollbar(self.details_frame, orient=tk.VERTICAL, command=self.details_canvas.yview)
        self.details_canvas.configure(yscrollcommand=scrollbar.set)
        
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.details_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Create a frame inside the canvas for the details
        self.details_content = ttk.Frame(self.details_canvas)
        self.details_canvas.create_window((0, 0), window=self.details_content, anchor=tk.NW)
        
        # Product ID (hidden)
        self.product_id_var = tk.StringVar()
        
        # Basic information
        basic_frame = ttk.LabelFrame(self.details_content, text="معلومات أساسية", padding=10)
        basic_frame.pack(fill=tk.X, pady=5)
        
        # Barcode
        ttk.Label(basic_frame, text="الباركود:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.barcode_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.barcode_var, width=30).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Name
        ttk.Label(basic_frame, text="اسم المنتج:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.name_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.name_var, width=30).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Description
        ttk.Label(basic_frame, text="الوصف:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.description_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.description_var, width=30).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Category
        ttk.Label(basic_frame, text="التصنيف:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.product_category_var = tk.StringVar()
        self.category_dropdown = ttk.Combobox(basic_frame, textvariable=self.product_category_var, values=self.categories[1:], width=20)
        self.category_dropdown.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Pricing information
        pricing_frame = ttk.LabelFrame(self.details_content, text="معلومات التسعير", padding=10)
        pricing_frame.pack(fill=tk.X, pady=5)
        
        # Purchase price
        ttk.Label(pricing_frame, text="سعر الشراء:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.purchase_price_var = tk.StringVar()
        ttk.Entry(pricing_frame, textvariable=self.purchase_price_var, width=15).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Selling price
        ttk.Label(pricing_frame, text="سعر البيع:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.selling_price_var = tk.StringVar()
        ttk.Entry(pricing_frame, textvariable=self.selling_price_var, width=15).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Inventory information
        inventory_frame = ttk.LabelFrame(self.details_content, text="معلومات المخزون", padding=10)
        inventory_frame.pack(fill=tk.X, pady=5)
        
        # Quantity
        ttk.Label(inventory_frame, text="الكمية الحالية:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.quantity_var = tk.StringVar()
        ttk.Entry(inventory_frame, textvariable=self.quantity_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Min quantity
        ttk.Label(inventory_frame, text="الحد الأدنى للكمية:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.min_quantity_var = tk.StringVar()
        ttk.Entry(inventory_frame, textvariable=self.min_quantity_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Supplier
        ttk.Label(inventory_frame, text="المورد:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.supplier_var = tk.StringVar()
        ttk.Entry(inventory_frame, textvariable=self.supplier_var, width=20).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Image
        image_frame = ttk.LabelFrame(self.details_content, text="صورة المنتج", padding=10)
        image_frame.pack(fill=tk.X, pady=5)
        
        # Image path (hidden)
        self.image_path_var = tk.StringVar()
        
        # Image preview
        self.image_label = ttk.Label(image_frame, text="لا توجد صورة")
        self.image_label.pack(pady=10)
        
        # Browse image button
        self.browse_image_btn = ttk.Button(image_frame, text="اختيار صورة", command=self.browse_image)
        self.browse_image_btn.pack(pady=5)
        
        # Action buttons
        action_frame = ttk.Frame(self.details_content)
        action_frame.pack(fill=tk.X, pady=10)
        
        self.save_btn = ttk.Button(action_frame, text="حفظ التغييرات", command=self.save_product)
        self.save_btn.pack(side=tk.LEFT, padx=5)
        
        self.cancel_btn = ttk.Button(action_frame, text="إلغاء", command=self.cancel_edit)
        self.cancel_btn.pack(side=tk.LEFT, padx=5)
        
        # Update the scroll region when the details content changes size
        self.details_content.bind("<Configure>", lambda e: self.details_canvas.configure(scrollregion=self.details_canvas.bbox("all")))
        
        # Initially disable the form
        self.set_form_state(tk.DISABLED)
    
    def load_products(self, products=None):
        """Load products into the treeview"""
        # Clear existing products
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)
        
        # Get products if not provided
        if products is None:
            products = self.product_model.get_all_products()
        
        # Add products to treeview
        for product in products:
            # Determine status
            if product[7] <= 0:
                status = "نفذت الكمية"
                status_tag = "out_of_stock"
            elif product[7] <= product[8]:
                status = "منخفض المخزون"
                status_tag = "low_stock"
            else:
                status = "متوفر"
                status_tag = "in_stock"
            
            # Add to treeview
            self.products_tree.insert('', tk.END, values=(
                product[0],  # id
                product[1],  # barcode
                product[2],  # name
                product[4],  # category
                f"{product[6]:.2f}",  # selling_price
                product[7],  # quantity
                status  # status
            ), tags=(status_tag,))
        
        # Configure tags
        self.products_tree.tag_configure('out_of_stock', background='#ffcccc')
        self.products_tree.tag_configure('low_stock', background='#ffffcc')
        self.products_tree.tag_configure('in_stock', background='#ccffcc')
    
    def on_product_select(self, event):
        """Handle product selection"""
        selection = self.products_tree.selection()
        if not selection:
            return
        
        # Get selected product ID
        product_id = self.products_tree.item(selection[0], 'values')[0]
        
        # Get product details
        product = self.product_model.get_product_by_id(product_id)
        if not product:
            return
        
        # Enable the form
        self.set_form_state(tk.NORMAL)
        
        # Fill the form with product details
        self.product_id_var.set(product[0])
        self.barcode_var.set(product[1] if product[1] else "")
        self.name_var.set(product[2])
        self.description_var.set(product[3] if product[3] else "")
        self.product_category_var.set(product[4] if product[4] else "")
        self.purchase_price_var.set(f"{product[5]:.2f}")
        self.selling_price_var.set(f"{product[6]:.2f}")
        self.quantity_var.set(product[7])
        self.min_quantity_var.set(product[8])
        self.image_path_var.set(product[9] if product[9] else "")
        self.supplier_var.set(product[14] if product[14] else "")  # supplier_name
        
        # Load image if available
        self.load_product_image()
    
    def load_product_image(self):
        """Load and display the product image"""
        image_path = self.image_path_var.get()
        if image_path and os.path.exists(image_path):
            try:
                # Open and resize image
                img = Image.open(image_path)
                img = img.resize((150, 150), Image.LANCZOS)
                photo = ImageTk.PhotoImage(img)
                
                # Update image label
                self.image_label.configure(image=photo, text="")
                self.image_label.image = photo  # Keep a reference
            except Exception as e:
                print(f"Error loading image: {e}")
                self.image_label.configure(image="", text="خطأ في تحميل الصورة")
        else:
            self.image_label.configure(image="", text="لا توجد صورة")
    
    def browse_image(self):
        """Browse for product image"""
        file_path = filedialog.askopenfilename(
            title="اختر صورة المنتج",
            filetypes=[("Image files", "*.jpg *.jpeg *.png *.gif"), ("All files", "*.*")]
        )
        if file_path:
            self.image_path_var.set(file_path)
            self.load_product_image()
    
    def set_form_state(self, state):
        """Enable or disable the form fields"""
        for child in self.details_content.winfo_children():
            if isinstance(child, ttk.LabelFrame):
                for grandchild in child.winfo_children():
                    if isinstance(grandchild, (ttk.Entry, ttk.Combobox)):
                        grandchild.configure(state=state)
        
        # Enable/disable buttons
        if state == tk.DISABLED:
            self.save_btn.configure(state=tk.DISABLED)
            self.cancel_btn.configure(state=tk.DISABLED)
            self.browse_image_btn.configure(state=tk.DISABLED)
        else:
            self.save_btn.configure(state=tk.NORMAL)
            self.cancel_btn.configure(state=tk.NORMAL)
            self.browse_image_btn.configure(state=tk.NORMAL)
    
    def clear_form(self):
        """Clear the form fields"""
        self.product_id_var.set("")
        self.barcode_var.set("")
        self.name_var.set("")
        self.description_var.set("")
        self.product_category_var.set("")
        self.purchase_price_var.set("")
        self.selling_price_var.set("")
        self.quantity_var.set("")
        self.min_quantity_var.set("")
        self.image_path_var.set("")
        self.supplier_var.set("")
        self.image_label.configure(image="", text="لا توجد صورة")
    
    def add_product(self):
        """Add a new product"""
        # Clear and enable the form
        self.clear_form()
        self.set_form_state(tk.NORMAL)
        
        # Set default values
        self.min_quantity_var.set("5")
        self.quantity_var.set("0")
        
        # Clear selection in treeview
        self.products_tree.selection_remove(self.products_tree.selection())
    
    def edit_product(self):
        """Edit the selected product"""
        selection = self.products_tree.selection()
        if not selection:
            messagebox.showinfo("تنبيه", "الرجاء اختيار منتج أولاً")
            return
        
        # Enable the form
        self.set_form_state(tk.NORMAL)
    
    def save_product(self):
        """Save the product (add new or update existing)"""
        # Validate form
        if not self.validate_form():
            return
        
        try:
            # Get form values
            product_id = self.product_id_var.get()
            barcode = self.barcode_var.get()
            name = self.name_var.get()
            description = self.description_var.get()
            category = self.product_category_var.get()
            purchase_price = float(self.purchase_price_var.get())
            selling_price = float(self.selling_price_var.get())
            quantity = int(self.quantity_var.get())
            min_quantity = int(self.min_quantity_var.get())
            image_path = self.image_path_var.get()
            
            # Get supplier ID (would need to be implemented properly)
            supplier_id = None
            
            if product_id:  # Update existing product
                success = self.product_model.update_product(
                    product_id, barcode, name, description, category,
                    purchase_price, selling_price, min_quantity, image_path, supplier_id
                )
                
                if success:
                    messagebox.showinfo("نجاح", "تم تحديث المنتج بنجاح")
                else:
                    messagebox.showerror("خطأ", "فشل تحديث المنتج")
            else:  # Add new product
                new_id = self.product_model.add_product(
                    barcode, name, description, category, purchase_price,
                    selling_price, quantity, min_quantity, image_path, supplier_id
                )
                
                if new_id:
                    messagebox.showinfo("نجاح", "تم إضافة المنتج بنجاح")
                else:
                    messagebox.showerror("خطأ", "فشل إضافة المنتج")
            
            # Refresh products list
            self.refresh_products()
            
            # Disable form
            self.set_form_state(tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ المنتج: {e}")
    
    def validate_form(self):
        """Validate the form fields"""
        # Check required fields
        if not self.name_var.get():
            messagebox.showwarning("تحذير", "الرجاء إدخال اسم المنتج")
            return False
        
        if not self.product_category_var.get():
            messagebox.showwarning("تحذير", "الرجاء اختيار تصنيف المنتج")
            return False
        
        # Validate numeric fields
        try:
            if self.purchase_price_var.get():
                float(self.purchase_price_var.get())
            
            if self.selling_price_var.get():
                float(self.selling_price_var.get())
            
            if self.quantity_var.get():
                int(self.quantity_var.get())
            
            if self.min_quantity_var.get():
                int(self.min_quantity_var.get())
        except ValueError:
            messagebox.showwarning("تحذير", "الرجاء إدخال قيم صحيحة للأسعار والكميات")
            return False
        
        return True
    
    def cancel_edit(self):
        """Cancel editing and disable the form"""
        self.set_form_state(tk.DISABLED)
        
        # If a product is selected, reload its details
        selection = self.products_tree.selection()
        if selection:
            self.on_product_select(None)
        else:
            self.clear_form()
    
    def delete_product(self):
        """Delete the selected product"""
        selection = self.products_tree.selection()
        if not selection:
            messagebox.showinfo("تنبيه", "الرجاء اختيار منتج أولاً")
            return
        
        product_id = self.products_tree.item(selection[0], 'values')[0]
        product_name = self.products_tree.item(selection[0], 'values')[2]
        
        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المنتج '{product_name}'؟"):
            success = self.product_model.delete_product(product_id)
            
            if success:
                messagebox.showinfo("نجاح", "تم حذف المنتج بنجاح")
                self.refresh_products()
                self.clear_form()
                self.set_form_state(tk.DISABLED)
            else:
                messagebox.showerror("خطأ", "فشل حذف المنتج")
    
    def search_products(self):
        """Search products by name or barcode"""
        search_term = self.search_var.get().strip()
        if search_term:
            products = self.product_model.search_products(search_term)
            self.filter_products(products)
        else:
            self.refresh_products()
    
    def filter_by_category(self, event=None):
        """Filter products by category"""
        self.filter_products()
    
    def filter_products(self, products=None):
        """Apply filters to products list"""
        # Get all products if not provided
        if products is None:
            products = self.product_model.get_all_products()
        
        # Apply category filter
        category = self.category_var.get()
        if category != "الكل":
            products = [p for p in products if p[4] == category]
        
        # Apply low stock filter
        if self.low_stock_var.get():
            products = [p for p in products if p[7] <= p[8]]
        
        # Load filtered products
        self.load_products(products)
    
    def refresh_products(self):
        """Refresh the products list"""
        self.search_var.set("")
        self.category_var.set("الكل")
        self.low_stock_var.set(False)
        self.load_products()


# For testing the products window independently
if __name__ == "__main__":
    import sqlite3
    from database.db_setup import DatabaseSetup
    
    # Setup database
    db = DatabaseSetup()
    db.connect()
    db.setup_tables()
    db.insert_sample_data()
    
    # Create root window
    root = tk.Tk()
    
    # Mock user for testing
    mock_user = (1, 'admin', 'admin123', 'System Administrator', 'admin', 'day', None, None, None, None, 1)
    
    # Create products window
    products_window = ProductsWindow(root, db.conn, mock_user)
    
    # Start the main loop
    root.mainloop()
    
    # Close database connection
    db.close()