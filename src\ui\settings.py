import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import os
import sys

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from models.user import User

class SettingsWindow:
    def __init__(self, root, db_connection, current_user):
        """Initialize the settings window"""
        self.root = root
        self.conn = db_connection
        self.current_user = current_user
        
        # Initialize models
        self.user_model = User(self.conn)
        
        # Check if user has admin privileges
        self.is_admin = (current_user[4] == 'admin')
        
        # Configure the window
        self.root.title(f"POSMADORA - الإعدادات - {current_user[3]}")
        self.root.geometry("800x600")
        
        # Set window icon if available
        try:
            self.root.iconbitmap("resources/images/icon.ico")
        except:
            pass
        
        # Create a style
        self.style = ttk.Style()
        self.style.configure('TLabel', font=('Arial', 12))
        self.style.configure('TButton', font=('Arial', 12))
        self.style.configure('Header.TLabel', font=('Arial', 18, 'bold'))
        self.style.configure('Title.TLabel', font=('Arial', 14, 'bold'))
        
        # Create main container
        self.main_container = ttk.Frame(self.root, padding=10)
        self.main_container.pack(fill=tk.BOTH, expand=True)
        
        # Create header
        self.header_label = ttk.Label(self.main_container, text="إعدادات النظام", style='Header.TLabel')
        self.header_label.pack(pady=(0, 20))
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.main_container)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Create tabs
        self.create_general_tab()
        self.create_users_tab()
        self.create_backup_tab()
        self.create_about_tab()
        
        # Create bottom buttons
        self.button_frame = ttk.Frame(self.main_container)
        self.button_frame.pack(fill=tk.X, pady=10)
        
        self.save_btn = ttk.Button(self.button_frame, text="حفظ التغييرات", command=self.save_settings)
        self.save_btn.pack(side=tk.RIGHT, padx=5)
        
        self.cancel_btn = ttk.Button(self.button_frame, text="إلغاء", command=self.root.destroy)
        self.cancel_btn.pack(side=tk.RIGHT, padx=5)
        
        # Load current settings
        self.load_settings()
    
    def create_general_tab(self):
        """Create the general settings tab"""
        self.general_tab = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(self.general_tab, text="إعدادات عامة")
        
        # Store information
        store_frame = ttk.LabelFrame(self.general_tab, text="معلومات المتجر", padding=10)
        store_frame.pack(fill=tk.X, pady=5)
        
        # Store name
        ttk.Label(store_frame, text="اسم المتجر:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.store_name_var = tk.StringVar()
        ttk.Entry(store_frame, textvariable=self.store_name_var, width=40).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Store address
        ttk.Label(store_frame, text="عنوان المتجر:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.store_address_var = tk.StringVar()
        ttk.Entry(store_frame, textvariable=self.store_address_var, width=40).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Store phone
        ttk.Label(store_frame, text="رقم الهاتف:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.store_phone_var = tk.StringVar()
        ttk.Entry(store_frame, textvariable=self.store_phone_var, width=40).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Receipt footer
        ttk.Label(store_frame, text="تذييل الفاتورة:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.receipt_footer_var = tk.StringVar()
        ttk.Entry(store_frame, textvariable=self.receipt_footer_var, width=40).grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Tax settings
        tax_frame = ttk.LabelFrame(self.general_tab, text="إعدادات الضريبة", padding=10)
        tax_frame.pack(fill=tk.X, pady=5)
        
        # Tax rate
        ttk.Label(tax_frame, text="نسبة الضريبة (%):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.tax_rate_var = tk.StringVar()
        ttk.Entry(tax_frame, textvariable=self.tax_rate_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Enable tax
        self.enable_tax_var = tk.BooleanVar()
        ttk.Checkbutton(tax_frame, text="تفعيل الضريبة", variable=self.enable_tax_var).grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        
        # Display settings
        display_frame = ttk.LabelFrame(self.general_tab, text="إعدادات العرض", padding=10)
        display_frame.pack(fill=tk.X, pady=5)
        
        # Currency symbol
        ttk.Label(display_frame, text="رمز العملة:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.currency_symbol_var = tk.StringVar()
        ttk.Entry(display_frame, textvariable=self.currency_symbol_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Theme
        ttk.Label(display_frame, text="السمة:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.theme_var = tk.StringVar()
        ttk.Combobox(display_frame, textvariable=self.theme_var, values=["light", "dark"], width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
    
    def create_users_tab(self):
        """Create the users management tab"""
        self.users_tab = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(self.users_tab, text="إدارة المستخدمين")
        
        # Check if user has admin privileges
        if not self.is_admin:
            ttk.Label(self.users_tab, text="عذراً، لا تملك صلاحيات للوصول إلى هذه الصفحة", style='Title.TLabel').pack(pady=50)
            return
        
        # Users list
        users_frame = ttk.LabelFrame(self.users_tab, text="قائمة المستخدمين", padding=10)
        users_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # Create treeview for users
        columns = ('id', 'username', 'name', 'role', 'shift', 'last_login')
        self.users_tree = ttk.Treeview(users_frame, columns=columns, show='headings', height=10)
        
        # Define headings
        self.users_tree.heading('id', text='#')
        self.users_tree.heading('username', text='اسم المستخدم')
        self.users_tree.heading('name', text='الاسم الكامل')
        self.users_tree.heading('role', text='الدور')
        self.users_tree.heading('shift', text='الوردية')
        self.users_tree.heading('last_login', text='آخر تسجيل دخول')
        
        # Define columns
        self.users_tree.column('id', width=30, anchor=tk.CENTER)
        self.users_tree.column('username', width=100)
        self.users_tree.column('name', width=150)
        self.users_tree.column('role', width=100)
        self.users_tree.column('shift', width=80)
        self.users_tree.column('last_login', width=150)
        
        # Add scrollbar
        users_scrollbar = ttk.Scrollbar(users_frame, orient=tk.VERTICAL, command=self.users_tree.yview)
        self.users_tree.configure(yscroll=users_scrollbar.set)
        users_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.users_tree.pack(fill=tk.BOTH, expand=True)
        
        # User actions
        actions_frame = ttk.Frame(self.users_tab)
        actions_frame.pack(fill=tk.X, pady=5)
        
        self.add_user_btn = ttk.Button(actions_frame, text="إضافة مستخدم", command=self.add_user)
        self.add_user_btn.pack(side=tk.LEFT, padx=5)
        
        self.edit_user_btn = ttk.Button(actions_frame, text="تعديل مستخدم", command=self.edit_user)
        self.edit_user_btn.pack(side=tk.LEFT, padx=5)
        
        self.delete_user_btn = ttk.Button(actions_frame, text="حذف مستخدم", command=self.delete_user)
        self.delete_user_btn.pack(side=tk.LEFT, padx=5)
        
        self.reset_password_btn = ttk.Button(actions_frame, text="إعادة تعيين كلمة المرور", command=self.reset_password)
        self.reset_password_btn.pack(side=tk.LEFT, padx=5)
        
        # Load users
        self.load_users()
    
    def create_backup_tab(self):
        """Create the backup and restore tab"""
        self.backup_tab = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(self.backup_tab, text="النسخ الاحتياطي")
        
        # Backup section
        backup_frame = ttk.LabelFrame(self.backup_tab, text="إنشاء نسخة احتياطية", padding=10)
        backup_frame.pack(fill=tk.X, pady=5)
        
        # Backup path
        ttk.Label(backup_frame, text="مسار النسخ الاحتياطي:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.backup_path_var = tk.StringVar()
        ttk.Entry(backup_frame, textvariable=self.backup_path_var, width=40).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        self.browse_backup_btn = ttk.Button(backup_frame, text="استعراض...", command=self.browse_backup_path)
        self.browse_backup_btn.grid(row=0, column=2, padx=5, pady=5)
        
        # Create backup button
        self.create_backup_btn = ttk.Button(backup_frame, text="إنشاء نسخة احتياطية الآن", command=self.create_backup)
        self.create_backup_btn.grid(row=1, column=1, padx=5, pady=10)
        
        # Restore section
        restore_frame = ttk.LabelFrame(self.backup_tab, text="استعادة من نسخة احتياطية", padding=10)
        restore_frame.pack(fill=tk.X, pady=5)
        
        # Restore file
        ttk.Label(restore_frame, text="ملف النسخة الاحتياطية:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.restore_file_var = tk.StringVar()
        ttk.Entry(restore_frame, textvariable=self.restore_file_var, width=40).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        self.browse_restore_btn = ttk.Button(restore_frame, text="استعراض...", command=self.browse_restore_file)
        self.browse_restore_btn.grid(row=0, column=2, padx=5, pady=5)
        
        # Restore button
        self.restore_btn = ttk.Button(restore_frame, text="استعادة من النسخة الاحتياطية", command=self.restore_backup)
        self.restore_btn.grid(row=1, column=1, padx=5, pady=10)
        
        # Auto backup section
        auto_backup_frame = ttk.LabelFrame(self.backup_tab, text="النسخ الاحتياطي التلقائي", padding=10)
        auto_backup_frame.pack(fill=tk.X, pady=5)
        
        # Enable auto backup
        self.auto_backup_var = tk.BooleanVar()
        ttk.Checkbutton(auto_backup_frame, text="تفعيل النسخ الاحتياطي التلقائي", variable=self.auto_backup_var).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        
        # Auto backup frequency
        ttk.Label(auto_backup_frame, text="تكرار النسخ الاحتياطي:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.backup_frequency_var = tk.StringVar()
        ttk.Combobox(auto_backup_frame, textvariable=self.backup_frequency_var, values=["يومي", "أسبوعي", "شهري"], width=15).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
    
    def create_about_tab(self):
        """Create the about tab"""
        self.about_tab = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(self.about_tab, text="حول البرنامج")
        
        # App info
        ttk.Label(self.about_tab, text="POSMADORA", font=('Arial', 24, 'bold')).pack(pady=(20, 5))
        ttk.Label(self.about_tab, text="نظام نقاط البيع المتكامل", font=('Arial', 16)).pack(pady=5)
        ttk.Label(self.about_tab, text="الإصدار 1.0", font=('Arial', 12)).pack(pady=5)
        
        # Description
        description = """
        نظام POSMADORA هو نظام نقاط بيع متكامل يوفر:
        - إدارة المبيعات والفواتير
        - إدارة المخزون والمنتجات
        - إدارة العملاء وبرامج الولاء
        - إدارة الموظفين والورديات
        - تقارير مفصلة للمبيعات والمخزون
        - نظام نسخ احتياطي متكامل
        """
        ttk.Label(self.about_tab, text=description, justify=tk.CENTER, wraplength=500).pack(pady=20)
        
        # Copyright
        ttk.Label(self.about_tab, text="© 2024 جميع الحقوق محفوظة", font=('Arial', 10)).pack(pady=5)
        
        # Contact info
        ttk.Label(self.about_tab, text="للدعم الفني: <EMAIL>", font=('Arial', 10)).pack(pady=5)
    
    def load_settings(self):
        """Load current settings from database"""
        try:
            # Get settings from database
            cursor = self.conn.cursor()
            cursor.execute("SELECT setting_key, setting_value FROM settings")
            settings = {row[0]: row[1] for row in cursor.fetchall()}
            
            # Set values in UI
            self.store_name_var.set(settings.get('store_name', 'POSMADORA'))
            self.store_address_var.set(settings.get('store_address', ''))
            self.store_phone_var.set(settings.get('store_phone', ''))
            self.receipt_footer_var.set(settings.get('receipt_footer', 'شكراً لتسوقكم معنا!'))
            self.tax_rate_var.set(settings.get('tax_rate', '15'))
            self.enable_tax_var.set(settings.get('enable_tax', '1') == '1')
            self.currency_symbol_var.set(settings.get('currency_symbol', '$'))
            self.theme_var.set(settings.get('theme', 'light'))
            self.backup_path_var.set(settings.get('backup_path', './backups'))
            self.auto_backup_var.set(settings.get('auto_backup', '0') == '1')
            self.backup_frequency_var.set(settings.get('backup_frequency', 'يومي'))
            
        except sqlite3.Error as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل الإعدادات: {e}")
    
    def load_users(self):
        """Load users into the treeview"""
        if not self.is_admin:
            return
            
        try:
            # Clear existing items
            self.users_tree.delete(*self.users_tree.get_children())
            
            # Get all users
            users = self.user_model.get_all_users()
            
            # Add to treeview
            for user in users:
                # Format last login
                last_login = user[9] if user[9] else "لم يسجل الدخول بعد"
                
                self.users_tree.insert('', tk.END, values=(
                    user[0],  # id
                    user[1],  # username
                    user[3],  # full_name
                    user[4],  # role
                    "نهاري" if user[5] == "day" else "ليلي",  # shift
                    last_login  # last_login
                ))
                
        except sqlite3.Error as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل المستخدمين: {e}")
    
    def save_settings(self):
        """Save settings to database"""
        try:
            cursor = self.conn.cursor()
            
            # Update settings
            settings = {
                'store_name': self.store_name_var.get(),
                'store_address': self.store_address_var.get(),
                'store_phone': self.store_phone_var.get(),
                'receipt_footer': self.receipt_footer_var.get(),
                'tax_rate': self.tax_rate_var.get(),
                'enable_tax': '1' if self.enable_tax_var.get() else '0',
                'currency_symbol': self.currency_symbol_var.get(),
                'theme': self.theme_var.get(),
                'backup_path': self.backup_path_var.get(),
                'auto_backup': '1' if self.auto_backup_var.get() else '0',
                'backup_frequency': self.backup_frequency_var.get()
            }
            
            for key, value in settings.items():
                cursor.execute(
                    "UPDATE settings SET setting_value = ?, last_updated = CURRENT_TIMESTAMP WHERE setting_key = ?",
                    (value, key)
                )
            
            self.conn.commit()
            messagebox.showinfo("نجاح", "تم حفظ الإعدادات بنجاح")
            
        except sqlite3.Error as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ الإعدادات: {e}")
    
    def add_user(self):
        """Add a new user"""
        if not self.is_admin:
            return
            
        # This would open a dialog to add a new user
        messagebox.showinfo("إضافة مستخدم", "سيتم فتح نافذة إضافة مستخدم جديد")
    
    def edit_user(self):
        """Edit selected user"""
        if not self.is_admin:
            return
            
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showinfo("تنبيه", "الرجاء اختيار مستخدم أولاً")
            return
            
        # This would open a dialog to edit the selected user
        user_id = self.users_tree.item(selection[0], 'values')[0]
        messagebox.showinfo("تعديل مستخدم", f"سيتم فتح نافذة تعديل المستخدم رقم {user_id}")
    
    def delete_user(self):
        """Delete selected user"""
        if not self.is_admin:
            return
            
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showinfo("تنبيه", "الرجاء اختيار مستخدم أولاً")
            return
            
        user_id = self.users_tree.item(selection[0], 'values')[0]
        username = self.users_tree.item(selection[0], 'values')[1]
        
        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المستخدم {username}؟"):
            try:
                self.user_model.delete_user(user_id)
                self.users_tree.delete(selection[0])
                messagebox.showinfo("نجاح", f"تم حذف المستخدم {username} بنجاح")
            except sqlite3.Error as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء حذف المستخدم: {e}")
    
    def reset_password(self):
        """Reset password for selected user"""
        if not self.is_admin:
            return
            
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showinfo("تنبيه", "الرجاء اختيار مستخدم أولاً")
            return
            
        user_id = self.users_tree.item(selection[0], 'values')[0]
        username = self.users_tree.item(selection[0], 'values')[1]
        
        if messagebox.askyesno("تأكيد إعادة تعيين", f"هل أنت متأكد من إعادة تعيين كلمة المرور للمستخدم {username}؟"):
            # This would reset the password
            messagebox.showinfo("نجاح", f"تم إعادة تعيين كلمة المرور للمستخدم {username} بنجاح")
    
    def browse_backup_path(self):
        """Browse for backup directory"""
        path = filedialog.askdirectory(title="اختر مجلد النسخ الاحتياطي")
        if path:
            self.backup_path_var.set(path)
    
    def browse_restore_file(self):
        """Browse for backup file to restore"""
        file = filedialog.askopenfilename(
            title="اختر ملف النسخة الاحتياطية",
            filetypes=[("ملفات قاعدة البيانات", "*.db"), ("جميع الملفات", "*.*")]
        )
        if file:
            self.restore_file_var.set(file)
    
    def create_backup(self):
        """Create a database backup"""
        # This would implement backup functionality
        messagebox.showinfo("نسخ احتياطي", "تم إنشاء نسخة احتياطية بنجاح")
    
    def restore_backup(self):
        """Restore from a database backup"""
        if not self.restore_file_var.get():
            messagebox.showinfo("تنبيه", "الرجاء اختيار ملف النسخة الاحتياطية أولاً")
            return
            
        if messagebox.askyesno("تأكيد الاستعادة", "سيتم استبدال قاعدة البيانات الحالية. هل أنت متأكد من المتابعة؟"):
            # This would implement restore functionality
            messagebox.showinfo("استعادة", "تم استعادة قاعدة البيانات بنجاح")


# For testing the settings window independently
if __name__ == "__main__":
    import sqlite3
    from database.db_setup import DatabaseSetup
    
    # Setup database
    db = DatabaseSetup()
    db.connect()
    db.setup_tables()
    db.insert_sample_data()
    
    # Create root window
    root = tk.Tk()
    
    # Mock user for testing
    mock_user = (1, 'admin', 'admin123', 'System Administrator', 'admin', 'day', None, None, None, None, 1)
    
    # Create settings window
    settings_window = SettingsWindow(root, db.conn, mock_user)
    
    # Start the main loop
    root.mainloop()
    
    # Close database connection
    db.close()