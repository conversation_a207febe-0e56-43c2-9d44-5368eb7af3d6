import tkinter as tk
import os
import sys
import sqlite3
from PIL import Image, ImageTk

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

# Import modules
from src.database.db_setup import DatabaseSetup
from src.ui.login import <PERSON>ginWindow
from src.ui.dashboard import Dashboard
from src.ui.pos import POSWindow
from src.ui.settings import SettingsWindow
from src.ui.styles import AppStyles, ToastNotification, center_window, load_image

class POSMadoraApp:
    def __init__(self, root):
        """Initialize the main application"""
        self.root = root
        self.db = None
        self.conn = None
        self.current_user = None
        
        # Configure the root window
        self.root.title("POSMADORA")
        self.root.state('zoomed')  # Maximize window
        
        # Set window icon if available
        try:
            self.root.iconbitmap("resources/images/icon.ico")
        except:
            pass
        
        # Initialize database
        self.setup_database()
        
        # Show login screen
        self.show_login()
    
    def setup_database(self):
        """Set up the database connection"""
        try:
            self.db = DatabaseSetup()
            if self.db.connect():
                self.db.setup_tables()
                self.db.insert_sample_data()
                self.conn = self.db.conn
                print("Database setup complete")
            else:
                print("Failed to connect to database")
                self.root.destroy()
        except Exception as e:
            print(f"Database setup error: {e}")
            self.root.destroy()
    
    def show_login(self):
        """Show the login window"""
        # Clear the root window
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # Create login window
        self.login_window = LoginWindow(self.root, self.conn, self.on_login_success)
    
    def on_login_success(self, user):
        """Handle successful login"""
        self.current_user = user
        self.show_dashboard()
    
    def show_dashboard(self):
        """Show the dashboard"""
        # Clear the root window
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # Create dashboard
        self.dashboard = Dashboard(self.root, self.conn, self.current_user)
        
        # Add menu
        self.create_menu()
    
    def show_pos(self):
        """Show the POS screen"""
        # Create a new window for POS
        pos_window = tk.Toplevel(self.root)
        pos_window.title("POSMADORA - نقطة البيع")
        pos_window.state('zoomed')
        
        # Create POS screen
        POSWindow(pos_window, self.conn, self.current_user)
    
    def show_settings(self):
        """Show the settings screen"""
        # Create a new window for settings
        settings_window = tk.Toplevel(self.root)
        settings_window.title("POSMADORA - الإعدادات")
        settings_window.geometry("800x600")
        
        # Create settings screen
        SettingsWindow(settings_window, self.conn, self.current_user)
    
    def create_menu(self):
        """Create the application menu"""
        menubar = tk.Menu(self.root)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label="نقطة البيع", command=self.show_pos)
        file_menu.add_command(label="الإعدادات", command=self.show_settings)
        file_menu.add_separator()
        file_menu.add_command(label="تسجيل الخروج", command=self.show_login)
        file_menu.add_command(label="خروج", command=self.root.quit)
        menubar.add_cascade(label="ملف", menu=file_menu)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        help_menu.add_command(label="المساعدة", command=self.show_help)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        
        self.root.config(menu=menubar)
    
    def show_help(self):
        """Show help dialog"""
        help_window = tk.Toplevel(self.root)
        help_window.title("POSMADORA - المساعدة")
        help_window.geometry("600x400")
        help_window.configure(bg=AppStyles.BG_MAIN)
        
        # Center the window
        center_window(help_window, 600, 400)
        
        # Add help content
        header_frame = tk.Frame(help_window, bg=AppStyles.PRIMARY, padx=20, pady=20)
        header_frame.pack(fill=tk.X)
        
        tk.Label(header_frame, text="مساعدة POSMADORA", 
                font=(AppStyles.FONT_FAMILY_ARABIC, 18, 'bold'),
                fg=AppStyles.TEXT_LIGHT, bg=AppStyles.PRIMARY).pack()
        
        content_frame = tk.Frame(help_window, bg=AppStyles.BG_MAIN, padx=30, pady=30)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        help_text = """
        للمساعدة في استخدام نظام POSMADORA، يرجى الرجوع إلى دليل المستخدم
        أو الاتصال بالدعم الفني على:
        
        البريد الإلكتروني: <EMAIL>
        الهاتف: +123456789
        """
        
        tk.Label(content_frame, text=help_text, 
                font=(AppStyles.FONT_FAMILY_ARABIC, 12),
                justify=tk.CENTER, wraplength=500,
                fg=AppStyles.TEXT_DARK, bg=AppStyles.BG_MAIN).pack(pady=20)
        
        # Close button
        button_frame = tk.Frame(help_window, bg=AppStyles.BG_MAIN, pady=20)
        button_frame.pack()
        
        close_button = tk.Button(button_frame, text="إغلاق", 
                                font=(AppStyles.FONT_FAMILY_ARABIC, 12),
                                bg=AppStyles.PRIMARY, fg=AppStyles.TEXT_LIGHT,
                                padx=20, pady=5, bd=0,
                                command=help_window.destroy)
        close_button.pack()
    
    def show_about(self):
        """Show about dialog"""
        about_window = tk.Toplevel(self.root)
        about_window.title("POSMADORA - حول البرنامج")
        about_window.geometry("600x500")
        about_window.configure(bg=AppStyles.BG_MAIN)
        
        # Center the window
        center_window(about_window, 600, 500)
        
        # Add about content
        header_frame = tk.Frame(about_window, bg=AppStyles.PRIMARY, padx=20, pady=20)
        header_frame.pack(fill=tk.X)
        
        # Try to load logo
        try:
            logo_path = "resources/images/logo.png"
            logo_image = load_image(logo_path, 80, 80)
            if logo_image:
                logo_label = tk.Label(header_frame, image=logo_image, bg=AppStyles.PRIMARY)
                logo_label.image = logo_image  # Keep a reference
                logo_label.pack(pady=(0, 10))
        except Exception as e:
            print(f"Error loading logo: {e}")
        
        tk.Label(header_frame, text="POSMADORA", 
                font=(AppStyles.FONT_FAMILY, 24, 'bold'),
                fg=AppStyles.TEXT_LIGHT, bg=AppStyles.PRIMARY).pack()
        
        tk.Label(header_frame, text="نظام نقاط البيع المتكامل", 
                font=(AppStyles.FONT_FAMILY_ARABIC, 16),
                fg=AppStyles.TEXT_LIGHT, bg=AppStyles.PRIMARY).pack(pady=5)
        
        content_frame = tk.Frame(about_window, bg=AppStyles.BG_MAIN, padx=30, pady=30)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Version info
        version_frame = tk.Frame(content_frame, bg=AppStyles.BG_MAIN)
        version_frame.pack(fill=tk.X, pady=(0, 20))
        
        tk.Label(version_frame, text="الإصدار:", 
                font=(AppStyles.FONT_FAMILY_ARABIC, 12, 'bold'),
                fg=AppStyles.TEXT_DARK, bg=AppStyles.BG_MAIN).pack(side=tk.LEFT)
        
        tk.Label(version_frame, text="1.0", 
                font=(AppStyles.FONT_FAMILY, 12),
                fg=AppStyles.PRIMARY, bg=AppStyles.BG_MAIN).pack(side=tk.LEFT, padx=5)
        
        # Features
        features_frame = tk.Frame(content_frame, bg=AppStyles.BG_MAIN)
        features_frame.pack(fill=tk.X)
        
        tk.Label(features_frame, text="نظام POSMADORA هو نظام نقاط بيع متكامل يوفر:", 
                font=(AppStyles.FONT_FAMILY_ARABIC, 12),
                fg=AppStyles.TEXT_DARK, bg=AppStyles.BG_MAIN,
                justify=tk.RIGHT, anchor=tk.W).pack(fill=tk.X, pady=(0, 10))
        
        features = [
            "✓ إدارة المبيعات والفواتير",
            "✓ إدارة المخزون والمنتجات",
            "✓ إدارة العملاء وبرامج الولاء",
            "✓ إدارة الموظفين والورديات",
            "✓ تقارير مفصلة للمبيعات والمخزون",
            "✓ نظام نسخ احتياطي متكامل"
        ]
        
        for feature in features:
            tk.Label(features_frame, text=feature, 
                    font=(AppStyles.FONT_FAMILY_ARABIC, 12),
                    fg=AppStyles.TEXT_DARK, bg=AppStyles.BG_MAIN,
                    anchor=tk.W).pack(fill=tk.X, pady=2)
        
        # Copyright
        tk.Label(content_frame, text="© 2024 جميع الحقوق محفوظة", 
                font=(AppStyles.FONT_FAMILY_ARABIC, 10),
                fg=AppStyles.TEXT_MUTED, bg=AppStyles.BG_MAIN).pack(pady=(20, 0))
        
        # Close button
        button_frame = tk.Frame(about_window, bg=AppStyles.BG_MAIN, pady=20)
        button_frame.pack()
        
        close_button = tk.Button(button_frame, text="إغلاق", 
                                font=(AppStyles.FONT_FAMILY_ARABIC, 12),
                                bg=AppStyles.PRIMARY, fg=AppStyles.TEXT_LIGHT,
                                padx=20, pady=5, bd=0,
                                command=about_window.destroy)
        close_button.pack()
    
    def on_closing(self):
        """Handle application closing"""
        if self.conn:
            self.conn.close()
        self.root.destroy()


if __name__ == "__main__":
    # Create root window
    root = tk.Tk()
    
    # Create application
    app = POSMadoraApp(root)
    
    # Set up closing handler
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    
    # Start the main loop
    root.mainloop()