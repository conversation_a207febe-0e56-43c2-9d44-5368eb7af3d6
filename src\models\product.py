import sqlite3
import datetime

class Product:
    def __init__(self, db_connection):
        """Initialize Product model with database connection"""
        self.conn = db_connection
        self.cursor = self.conn.cursor()
    
    def get_all_products(self):
        """Get all products from the database"""
        try:
            self.cursor.execute('''
            SELECT p.*, s.name as supplier_name 
            FROM products p
            LEFT JOIN suppliers s ON p.supplier_id = s.id
            ''')
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error getting products: {e}")
            return []
    
    def get_product_by_id(self, product_id):
        """Get a product by ID"""
        try:
            self.cursor.execute('''
            SELECT p.*, s.name as supplier_name 
            FROM products p
            LEFT JOIN suppliers s ON p.supplier_id = s.id
            WHERE p.id = ?
            ''', (product_id,))
            return self.cursor.fetchone()
        except sqlite3.Error as e:
            print(f"Error getting product by ID: {e}")
            return None
    
    def get_product_by_barcode(self, barcode):
        """Get a product by barcode"""
        try:
            self.cursor.execute('''
            SELECT p.*, s.name as supplier_name 
            FROM products p
            LEFT JOIN suppliers s ON p.supplier_id = s.id
            WHERE p.barcode = ?
            ''', (barcode,))
            return self.cursor.fetchone()
        except sqlite3.Error as e:
            print(f"Error getting product by barcode: {e}")
            return None
    
    def search_products(self, search_term):
        """Search products by name, description, or barcode"""
        try:
            search_pattern = f"%{search_term}%"
            self.cursor.execute('''
            SELECT p.*, s.name as supplier_name 
            FROM products p
            LEFT JOIN suppliers s ON p.supplier_id = s.id
            WHERE p.name LIKE ? OR p.description LIKE ? OR p.barcode LIKE ?
            ''', (search_pattern, search_pattern, search_pattern))
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error searching products: {e}")
            return []
    
    def get_products_by_category(self, category):
        """Get all products in a specific category"""
        try:
            self.cursor.execute('''
            SELECT p.*, s.name as supplier_name 
            FROM products p
            LEFT JOIN suppliers s ON p.supplier_id = s.id
            WHERE p.category = ?
            ''', (category,))
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error getting products by category: {e}")
            return []
    
    def get_low_stock_products(self):
        """Get products with quantity below minimum quantity"""
        try:
            self.cursor.execute('''
            SELECT p.*, s.name as supplier_name 
            FROM products p
            LEFT JOIN suppliers s ON p.supplier_id = s.id
            WHERE p.quantity <= p.min_quantity
            ''')
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error getting low stock products: {e}")
            return []
    
    def add_product(self, barcode, name, description, category, purchase_price, 
                   selling_price, quantity, min_quantity, image_path=None, supplier_id=None):
        """Add a new product to the database"""
        try:
            self.cursor.execute('''
            INSERT INTO products (
                barcode, name, description, category, purchase_price, 
                selling_price, quantity, min_quantity, image_path, supplier_id
            )
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (barcode, name, description, category, purchase_price, 
                 selling_price, quantity, min_quantity, image_path, supplier_id))
            self.conn.commit()
            
            # Log inventory transaction
            product_id = self.cursor.lastrowid
            self.cursor.execute('''
            INSERT INTO inventory_transactions (
                product_id, transaction_type, quantity, user_id, notes
            )
            VALUES (?, ?, ?, ?, ?)
            ''', (product_id, 'initial', quantity, 1, 'Initial inventory'))
            self.conn.commit()
            
            return product_id
        except sqlite3.Error as e:
            print(f"Error adding product: {e}")
            return None
    
    def update_product(self, product_id, barcode=None, name=None, description=None, 
                      category=None, purchase_price=None, selling_price=None, 
                      min_quantity=None, image_path=None, supplier_id=None):
        """Update an existing product (excluding quantity which is handled separately)"""
        try:
            # Get current product data
            self.cursor.execute("SELECT * FROM products WHERE id = ?", (product_id,))
            product = self.cursor.fetchone()
            if not product:
                return False
            
            # Use current values if new ones not provided
            barcode = barcode if barcode is not None else product[1]
            name = name if name is not None else product[2]
            description = description if description is not None else product[3]
            category = category if category is not None else product[4]
            purchase_price = purchase_price if purchase_price is not None else product[5]
            selling_price = selling_price if selling_price is not None else product[6]
            min_quantity = min_quantity if min_quantity is not None else product[8]
            image_path = image_path if image_path is not None else product[9]
            supplier_id = supplier_id if supplier_id is not None else product[10]
            
            self.cursor.execute('''
            UPDATE products 
            SET barcode = ?, name = ?, description = ?, category = ?, 
                purchase_price = ?, selling_price = ?, min_quantity = ?, 
                image_path = ?, supplier_id = ?, last_updated = ?
            WHERE id = ?
            ''', (barcode, name, description, category, purchase_price, 
                 selling_price, min_quantity, image_path, supplier_id, 
                 datetime.datetime.now(), product_id))
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error updating product: {e}")
            return False
    
    def update_stock(self, product_id, quantity_change, user_id, transaction_type, notes=None):
        """Update product stock and log the transaction"""
        try:
            # Get current quantity
            self.cursor.execute("SELECT quantity FROM products WHERE id = ?", (product_id,))
            result = self.cursor.fetchone()
            if not result:
                return False
            
            current_quantity = result[0]
            new_quantity = current_quantity + quantity_change
            
            # Update product quantity
            self.cursor.execute('''
            UPDATE products 
            SET quantity = ?, last_updated = ?
            WHERE id = ?
            ''', (new_quantity, datetime.datetime.now(), product_id))
            
            # Log inventory transaction
            self.cursor.execute('''
            INSERT INTO inventory_transactions (
                product_id, transaction_type, quantity, user_id, notes
            )
            VALUES (?, ?, ?, ?, ?)
            ''', (product_id, transaction_type, quantity_change, user_id, notes))
            
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error updating stock: {e}")
            return False
    
    def delete_product(self, product_id):
        """Delete a product from the database"""
        try:
            self.cursor.execute("DELETE FROM products WHERE id = ?", (product_id,))
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error deleting product: {e}")
            return False
    
    def get_all_categories(self):
        """Get all product categories"""
        try:
            self.cursor.execute("SELECT * FROM categories")
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error getting categories: {e}")
            return []