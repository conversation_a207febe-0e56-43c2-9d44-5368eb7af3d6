# POSMADORA - نظام نقاط البيع المتكامل

POSMADORA هو نظام نقاط بيع متكامل مفتوح المصدر مكتوب بلغة Python، يوفر حلاً شاملاً لإدارة المبيعات والمخزون والعملاء والموظفين.

## المميزات الرئيسية

- **نظام نقاط البيع**: واجهة سهلة الاستخدام لإجراء المبيعات وإصدار الفواتير
- **إدارة المخزون**: تتبع المنتجات والمخزون وتنبيهات انخفاض المخزون
- **إدارة العملاء**: قاعدة بيانات للعملاء ونظام نقاط الولاء
- **إدارة الموظفين**: إدارة المستخدمين والصلاحيات والورديات (نهاري/ليلي)
- **التقارير**: تقارير مفصلة للمبيعات والمخزون والعملاء والموظفين
- **النسخ الاحتياطي**: نظام متكامل للنسخ الاحتياطي واستعادة البيانات

## متطلبات النظام

- Python 3.6 أو أحدث
- Tkinter (مضمن مع معظم توزيعات Python)
- SQLite3 (مضمن مع Python)
- matplotlib (لعرض الرسوم البيانية)

## التثبيت

1. قم بتنزيل أو استنساخ المشروع:
```
git clone https://github.com/yourusername/posmadora.git
cd posmadora
```

2. قم بتثبيت المكتبات المطلوبة:
```
pip install -r requirements.txt

Python -m pip install matplotlib pillow
```

3. قم بتشغيل البرنامج:
```
python main.py
```

## بدء الاستخدام

عند تشغيل البرنامج لأول مرة، سيتم إنشاء قاعدة بيانات جديدة وإضافة بيانات نموذجية للاختبار.

### بيانات تسجيل الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## هيكل المشروع

```
POSMADORA/
├── main.py                  # نقطة الدخول الرئيسية للبرنامج
├── requirements.txt         # قائمة المكتبات المطلوبة
├── README.md                # ملف التوثيق
├── resources/               # الموارد (الصور، الأيقونات، إلخ)
│   └── images/              # ملفات الصور
└── src/                     # كود المصدر
    ├── database/            # وحدات قاعدة البيانات
    │   └── db_setup.py      # إعداد قاعدة البيانات
    ├── models/              # نماذج البيانات
    │   ├── customer.py      # نموذج العملاء
    │   ├── product.py       # نموذج المنتجات
    │   ├── report.py        # نموذج التقارير
    │   ├── sale.py          # نموذج المبيعات
    │   └── user.py          # نموذج المستخدمين
    ├── ui/                  # واجهة المستخدم
    │   ├── dashboard.py     # لوحة التحكم
    │   ├── login.py         # شاشة تسجيل الدخول
    │   ├── pos.py           # شاشة نقطة البيع
    │   └── settings.py      # شاشة الإعدادات
    └── utils/               # أدوات مساعدة
```

## المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. قم بعمل Fork للمشروع
2. قم بإنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. قم بإجراء التغييرات وتسجيلها (`git commit -m 'Add some amazing feature'`)
4. قم بدفع التغييرات إلى الفرع (`git push origin feature/amazing-feature`)
5. قم بفتح طلب سحب (Pull Request)

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## الاتصال

للأسئلة والاستفسارات، يرجى التواصل عبر:
- البريد الإلكتروني: <EMAIL>
- الموقع الإلكتروني: https://www.posmadora.com