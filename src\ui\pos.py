import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import sqlite3
import os
import sys
import datetime

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from models.product import Product
from models.customer import Customer
from models.sale import Sale

class POSWindow:
    def __init__(self, root, db_connection, current_user):
        """Initialize the POS window"""
        self.root = root
        self.conn = db_connection
        self.current_user = current_user
        
        # Initialize models
        self.product_model = Product(self.conn)
        self.customer_model = Customer(self.conn)
        self.sale_model = Sale(self.conn)
        
        # Initialize sale variables
        self.current_sale_id = None
        self.current_invoice = None
        self.current_customer_id = None
        self.cart_items = []
        self.subtotal = 0.0
        self.discount = 0.0
        self.tax_rate = 0.15  # 15% tax rate
        self.tax_amount = 0.0
        self.total = 0.0
        
        # Configure the window
        self.root.title(f"POSMADORA - نقطة البيع - {current_user[3]}")
        self.root.state('zoomed')  # Maximize window
        
        # Set window icon if available
        try:
            self.root.iconbitmap("resources/images/icon.ico")
        except:
            pass
        
        # Create a style
        self.style = ttk.Style()
        self.style.configure('TLabel', font=('Arial', 12))
        self.style.configure('TButton', font=('Arial', 12))
        self.style.configure('Header.TLabel', font=('Arial', 18, 'bold'))
        self.style.configure('Title.TLabel', font=('Arial', 14, 'bold'))
        self.style.configure('Total.TLabel', font=('Arial', 16, 'bold'))
        
        # Create main container
        self.main_container = ttk.Frame(self.root)
        self.main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create top bar
        self.create_top_bar()
        
        # Create main content area with left and right panels
        self.content_frame = ttk.Frame(self.main_container)
        self.content_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # Create left panel (product catalog)
        self.left_panel = ttk.Frame(self.content_frame, width=600)
        self.left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # Create right panel (cart and checkout)
        self.right_panel = ttk.Frame(self.content_frame, width=400)
        self.right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, padx=(5, 0))
        
        # Create product catalog
        self.create_product_catalog()
        
        # Create shopping cart
        self.create_shopping_cart()
        
        # Create checkout panel
        self.create_checkout_panel()
        
        # Start a new sale
        self.new_sale()
    
    def create_top_bar(self):
        """Create the top bar with user info and date/time"""
        self.top_bar = ttk.Frame(self.main_container, height=60)
        self.top_bar.pack(fill=tk.X, pady=(0, 10))
        
        # Left side - Logo/Title
        self.title_label = ttk.Label(self.top_bar, text="POSMADORA - نقطة البيع", style='Header.TLabel')
        self.title_label.pack(side=tk.LEFT)
        
        # Right side - User info and date/time
        self.info_frame = ttk.Frame(self.top_bar)
        self.info_frame.pack(side=tk.RIGHT)
        
        # Current date and time
        self.datetime_var = tk.StringVar()
        self.update_datetime()
        self.datetime_label = ttk.Label(self.info_frame, textvariable=self.datetime_var, font=('Arial', 12))
        self.datetime_label.pack(pady=5)
        
        # User info
        self.user_label = ttk.Label(
            self.info_frame, 
            text=f"المستخدم: {self.current_user[3]} | الوردية: {self.current_user[5]}",
            font=('Arial', 12)
        )
        self.user_label.pack(pady=5)
        
        # Schedule datetime update
        self.root.after(1000, self.update_datetime)
        
        # Center - Quick actions
        self.actions_frame = ttk.Frame(self.top_bar)
        self.actions_frame.pack(side=tk.TOP, fill=tk.X, pady=5)
        
        # New Sale button
        self.new_sale_btn = ttk.Button(
            self.actions_frame, text="فاتورة جديدة", width=15,
            command=self.confirm_new_sale
        )
        self.new_sale_btn.pack(side=tk.LEFT, padx=5)
        
        # Hold Sale button
        self.hold_sale_btn = ttk.Button(
            self.actions_frame, text="تعليق الفاتورة", width=15,
            command=self.hold_sale
        )
        self.hold_sale_btn.pack(side=tk.LEFT, padx=5)
        
        # Recall Sale button
        self.recall_sale_btn = ttk.Button(
            self.actions_frame, text="استرجاع فاتورة", width=15,
            command=self.recall_sale
        )
        self.recall_sale_btn.pack(side=tk.LEFT, padx=5)
        
        # Void Sale button
        self.void_sale_btn = ttk.Button(
            self.actions_frame, text="إلغاء الفاتورة", width=15,
            command=self.void_sale
        )
        self.void_sale_btn.pack(side=tk.LEFT, padx=5)
        
        # Customer button
        self.customer_btn = ttk.Button(
            self.actions_frame, text="اختيار العميل", width=15,
            command=self.select_customer
        )
        self.customer_btn.pack(side=tk.LEFT, padx=5)
        
        # Dashboard button
        self.dashboard_btn = ttk.Button(
            self.actions_frame, text="لوحة التحكم", width=15,
            command=self.go_to_dashboard
        )
        self.dashboard_btn.pack(side=tk.LEFT, padx=5)
    
    def update_datetime(self):
        """Update the date and time display"""
        now = datetime.datetime.now()
        self.datetime_var.set(now.strftime("%Y-%m-%d %H:%M:%S"))
        self.root.after(1000, self.update_datetime)
    
    def create_product_catalog(self):
        """Create the product catalog panel"""
        # Create frame for search and categories
        self.catalog_top_frame = ttk.Frame(self.left_panel)
        self.catalog_top_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Search bar
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(self.catalog_top_frame, textvariable=self.search_var, width=30)
        self.search_entry.pack(side=tk.LEFT, padx=5)
        
        self.search_btn = ttk.Button(
            self.catalog_top_frame, text="بحث", width=10,
            command=self.search_products
        )
        self.search_btn.pack(side=tk.LEFT, padx=5)
        
        # Barcode entry
        ttk.Label(self.catalog_top_frame, text="الباركود:").pack(side=tk.LEFT, padx=(20, 5))
        self.barcode_var = tk.StringVar()
        self.barcode_entry = ttk.Entry(self.catalog_top_frame, textvariable=self.barcode_var, width=20)
        self.barcode_entry.pack(side=tk.LEFT, padx=5)
        self.barcode_entry.bind("<Return>", self.scan_barcode)
        
        # Category filter
        ttk.Label(self.catalog_top_frame, text="التصنيف:").pack(side=tk.LEFT, padx=(20, 5))
        self.category_var = tk.StringVar(value="الكل")
        self.categories = ["الكل"] + [cat[1] for cat in self.product_model.get_all_categories()]
        self.category_combo = ttk.Combobox(self.catalog_top_frame, textvariable=self.category_var, values=self.categories, width=15)
        self.category_combo.pack(side=tk.LEFT, padx=5)
        self.category_combo.bind("<<ComboboxSelected>>", self.filter_by_category)
        
        # Create product grid
        self.create_product_grid()
    
    def create_product_grid(self):
        """Create the product grid display"""
        # Create a frame for the product grid
        self.product_frame = ttk.LabelFrame(self.left_panel, text="المنتجات")
        self.product_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create a canvas with scrollbar for the products
        self.canvas = tk.Canvas(self.product_frame)
        self.scrollbar = ttk.Scrollbar(self.product_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Create a frame inside the canvas to hold the products
        self.product_grid = ttk.Frame(self.canvas)
        self.canvas.create_window((0, 0), window=self.product_grid, anchor=tk.NW)
        
        # Load products
        self.load_products()
        
        # Update the scroll region when the product grid changes size
        self.product_grid.bind("<Configure>", lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all")))
    
    def load_products(self, products=None):
        """Load products into the grid"""
        # Clear existing products
        for widget in self.product_grid.winfo_children():
            widget.destroy()
        
        # Get products if not provided
        if products is None:
            products = self.product_model.get_all_products()
        
        # Create product buttons in a grid
        row, col = 0, 0
        max_cols = 4  # Number of columns in the grid
        
        for product in products:
            # Create a frame for each product
            product_frame = ttk.Frame(self.product_grid, borderwidth=1, relief=tk.RAISED, padding=5)
            product_frame.grid(row=row, column=col, padx=5, pady=5, sticky=tk.NSEW)
            
            # Product name
            name_label = ttk.Label(product_frame, text=product[2], font=('Arial', 12, 'bold'), wraplength=150)
            name_label.pack(pady=(0, 5))
            
            # Product price
            price_label = ttk.Label(product_frame, text=f"السعر: {product[6]:.2f}")
            price_label.pack()
            
            # Product stock
            stock_label = ttk.Label(product_frame, text=f"المخزون: {product[7]}")
            stock_label.pack()
            
            # Add to cart button
            add_btn = ttk.Button(
                product_frame, text="إضافة للسلة",
                command=lambda p=product: self.add_to_cart(p)
            )
            add_btn.pack(pady=(5, 0))
            
            # Update row and column for next product
            col += 1
            if col >= max_cols:
                col = 0
                row += 1
    
    def search_products(self):
        """Search products by name or barcode"""
        search_term = self.search_var.get().strip()
        if search_term:
            products = self.product_model.search_products(search_term)
            self.load_products(products)
        else:
            self.load_products()
    
    def scan_barcode(self, event=None):
        """Handle barcode scanning"""
        barcode = self.barcode_var.get().strip()
        if barcode:
            product = self.product_model.get_product_by_barcode(barcode)
            if product:
                self.add_to_cart(product)
                self.barcode_var.set("")  # Clear barcode field
            else:
                messagebox.showwarning("باركود غير موجود", "لم يتم العثور على منتج بهذا الباركود")
    
    def filter_by_category(self, event=None):
        """Filter products by category"""
        category = self.category_var.get()
        if category == "الكل":
            self.load_products()
        else:
            products = self.product_model.get_products_by_category(category)
            self.load_products(products)
    
    def create_shopping_cart(self):
        """Create the shopping cart panel"""
        # Create a frame for the cart
        self.cart_frame = ttk.LabelFrame(self.right_panel, text="سلة المشتريات")
        self.cart_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Create treeview for cart items
        columns = ('id', 'name', 'price', 'quantity', 'total')
        self.cart_tree = ttk.Treeview(self.cart_frame, columns=columns, show='headings', height=10)
        
        # Define headings
        self.cart_tree.heading('id', text='#')
        self.cart_tree.heading('name', text='المنتج')
        self.cart_tree.heading('price', text='السعر')
        self.cart_tree.heading('quantity', text='الكمية')
        self.cart_tree.heading('total', text='المجموع')
        
        # Define columns
        self.cart_tree.column('id', width=30, anchor=tk.CENTER)
        self.cart_tree.column('name', width=200)
        self.cart_tree.column('price', width=80, anchor=tk.E)
        self.cart_tree.column('quantity', width=60, anchor=tk.CENTER)
        self.cart_tree.column('total', width=80, anchor=tk.E)
        
        # Add scrollbar
        cart_scrollbar = ttk.Scrollbar(self.cart_frame, orient=tk.VERTICAL, command=self.cart_tree.yview)
        self.cart_tree.configure(yscroll=cart_scrollbar.set)
        cart_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.cart_tree.pack(fill=tk.BOTH, expand=True)
        
        # Bind double-click to edit quantity
        self.cart_tree.bind("<Double-1>", self.edit_cart_item)
        
        # Create cart action buttons
        self.cart_actions = ttk.Frame(self.cart_frame)
        self.cart_actions.pack(fill=tk.X, pady=5)
        
        # Remove item button
        self.remove_btn = ttk.Button(
            self.cart_actions, text="حذف العنصر", width=15,
            command=self.remove_from_cart
        )
        self.remove_btn.pack(side=tk.LEFT, padx=5)
        
        # Clear cart button
        self.clear_btn = ttk.Button(
            self.cart_actions, text="تفريغ السلة", width=15,
            command=self.clear_cart
        )
        self.clear_btn.pack(side=tk.LEFT, padx=5)
        
        # Apply discount button
        self.discount_btn = ttk.Button(
            self.cart_actions, text="خصم", width=15,
            command=self.apply_discount
        )
        self.discount_btn.pack(side=tk.LEFT, padx=5)
    
    def create_checkout_panel(self):
        """Create the checkout panel"""
        # Create a frame for checkout
        self.checkout_frame = ttk.LabelFrame(self.right_panel, text="الدفع")
        self.checkout_frame.pack(fill=tk.BOTH, pady=(0, 10))
        
        # Customer info
        self.customer_frame = ttk.Frame(self.checkout_frame)
        self.customer_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(self.customer_frame, text="العميل:").pack(side=tk.LEFT, padx=5)
        self.customer_var = tk.StringVar(value="عميل عابر")
        self.customer_label = ttk.Label(self.customer_frame, textvariable=self.customer_var, font=('Arial', 12, 'bold'))
        self.customer_label.pack(side=tk.LEFT, padx=5)
        
        # Invoice info
        self.invoice_frame = ttk.Frame(self.checkout_frame)
        self.invoice_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(self.invoice_frame, text="رقم الفاتورة:").pack(side=tk.LEFT, padx=5)
        self.invoice_var = tk.StringVar()
        self.invoice_label = ttk.Label(self.invoice_frame, textvariable=self.invoice_var, font=('Arial', 12, 'bold'))
        self.invoice_label.pack(side=tk.LEFT, padx=5)
        
        # Totals
        self.totals_frame = ttk.Frame(self.checkout_frame)
        self.totals_frame.pack(fill=tk.X, pady=5)
        
        # Subtotal
        ttk.Label(self.totals_frame, text="المجموع الفرعي:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.subtotal_var = tk.StringVar(value="0.00")
        ttk.Label(self.totals_frame, textvariable=self.subtotal_var).grid(row=0, column=1, sticky=tk.E, padx=5, pady=2)
        
        # Discount
        ttk.Label(self.totals_frame, text="الخصم:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.discount_var = tk.StringVar(value="0.00")
        ttk.Label(self.totals_frame, textvariable=self.discount_var).grid(row=1, column=1, sticky=tk.E, padx=5, pady=2)
        
        # Tax
        ttk.Label(self.totals_frame, text="الضريبة:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.tax_var = tk.StringVar(value="0.00")
        ttk.Label(self.totals_frame, textvariable=self.tax_var).grid(row=2, column=1, sticky=tk.E, padx=5, pady=2)
        
        # Total
        ttk.Label(self.totals_frame, text="المجموع:", style='Total.TLabel').grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.total_var = tk.StringVar(value="0.00")
        ttk.Label(self.totals_frame, textvariable=self.total_var, style='Total.TLabel').grid(row=3, column=1, sticky=tk.E, padx=5, pady=5)
        
        # Payment methods
        self.payment_frame = ttk.LabelFrame(self.checkout_frame, text="طريقة الدفع")
        self.payment_frame.pack(fill=tk.X, pady=5)
        
        self.payment_var = tk.StringVar(value="cash")
        self.cash_radio = ttk.Radiobutton(self.payment_frame, text="نقدي", variable=self.payment_var, value="cash")
        self.cash_radio.pack(side=tk.LEFT, padx=10, pady=5)
        
        self.card_radio = ttk.Radiobutton(self.payment_frame, text="بطاقة ائتمان", variable=self.payment_var, value="card")
        self.card_radio.pack(side=tk.LEFT, padx=10, pady=5)
        
        self.bank_radio = ttk.Radiobutton(self.payment_frame, text="تحويل بنكي", variable=self.payment_var, value="bank")
        self.bank_radio.pack(side=tk.LEFT, padx=10, pady=5)
        
        # Checkout button
        self.checkout_btn = ttk.Button(
            self.checkout_frame, text="إتمام عملية الدفع", style='TButton',
            command=self.checkout
        )
        self.checkout_btn.pack(fill=tk.X, pady=10, padx=5)
    
    def new_sale(self):
        """Start a new sale"""
        # Create a new sale in the database
        sale_id, invoice = self.sale_model.create_sale(self.current_user[0])
        
        if sale_id:
            self.current_sale_id = sale_id
            self.current_invoice = invoice
            self.current_customer_id = None
            self.invoice_var.set(invoice)
            self.customer_var.set("عميل عابر")
            
            # Clear the cart
            self.cart_items = []
            self.cart_tree.delete(*self.cart_tree.get_children())
            
            # Reset totals
            self.subtotal = 0.0
            self.discount = 0.0
            self.tax_amount = 0.0
            self.total = 0.0
            self.update_totals()
            
            # Focus on barcode entry
            self.barcode_entry.focus_set()
        else:
            messagebox.showerror("خطأ", "فشل في إنشاء فاتورة جديدة")
    
    def confirm_new_sale(self):
        """Confirm starting a new sale if there are items in the cart"""
        if self.cart_items:
            if messagebox.askyesno("فاتورة جديدة", "هناك عناصر في السلة. هل أنت متأكد من بدء فاتورة جديدة؟"):
                self.new_sale()
        else:
            self.new_sale()
    
    def add_to_cart(self, product):
        """Add a product to the cart"""
        if not self.current_sale_id:
            self.new_sale()
        
        # Check if product is already in cart
        for i, item in enumerate(self.cart_items):
            if item['product_id'] == product[0]:
                # Update quantity
                item['quantity'] += 1
                item['total'] = item['quantity'] * item['price']
                
                # Update treeview
                item_id = self.cart_tree.get_children()[i]
                self.cart_tree.item(item_id, values=(
                    i+1, 
                    item['name'], 
                    f"{item['price']:.2f}", 
                    item['quantity'], 
                    f"{item['total']:.2f}"
                ))
                
                self.update_cart_totals()
                return
        
        # Add new item to cart
        item = {
            'product_id': product[0],
            'name': product[2],
            'price': product[6],
            'quantity': 1,
            'total': product[6]
        }
        self.cart_items.append(item)
        
        # Add to treeview
        self.cart_tree.insert('', tk.END, values=(
            len(self.cart_items), 
            item['name'], 
            f"{item['price']:.2f}", 
            item['quantity'], 
            f"{item['total']:.2f}"
        ))
        
        self.update_cart_totals()
    
    def edit_cart_item(self, event):
        """Edit the quantity of a cart item"""
        # Get selected item
        selection = self.cart_tree.selection()
        if not selection:
            return
        
        item_id = selection[0]
        item_index = self.cart_tree.index(item_id)
        
        # Ask for new quantity
        new_quantity = simpledialog.askinteger(
            "تعديل الكمية", 
            "أدخل الكمية الجديدة:",
            initialvalue=self.cart_items[item_index]['quantity'],
            minvalue=1,
            maxvalue=1000
        )
        
        if new_quantity:
            # Update cart item
            self.cart_items[item_index]['quantity'] = new_quantity
            self.cart_items[item_index]['total'] = new_quantity * self.cart_items[item_index]['price']
            
            # Update treeview
            self.cart_tree.item(item_id, values=(
                item_index+1, 
                self.cart_items[item_index]['name'], 
                f"{self.cart_items[item_index]['price']:.2f}", 
                new_quantity, 
                f"{self.cart_items[item_index]['total']:.2f}"
            ))
            
            self.update_cart_totals()
    
    def remove_from_cart(self):
        """Remove selected item from cart"""
        selection = self.cart_tree.selection()
        if not selection:
            messagebox.showinfo("تنبيه", "الرجاء اختيار عنصر من السلة أولاً")
            return
        
        item_id = selection[0]
        item_index = self.cart_tree.index(item_id)
        
        # Remove from cart items
        self.cart_items.pop(item_index)
        
        # Remove from treeview
        self.cart_tree.delete(item_id)
        
        # Update item numbers
        for i, child in enumerate(self.cart_tree.get_children()):
            values = self.cart_tree.item(child, 'values')
            self.cart_tree.item(child, values=(i+1,) + values[1:])
        
        self.update_cart_totals()
    
    def clear_cart(self):
        """Clear all items from the cart"""
        if not self.cart_items:
            return
            
        if messagebox.askyesno("تفريغ السلة", "هل أنت متأكد من تفريغ السلة؟"):
            self.cart_items = []
            self.cart_tree.delete(*self.cart_tree.get_children())
            self.update_cart_totals()
    
    def apply_discount(self):
        """Apply a discount to the sale"""
        if not self.cart_items:
            messagebox.showinfo("تنبيه", "لا توجد عناصر في السلة")
            return
        
        # Ask for discount amount
        discount = simpledialog.askfloat(
            "تطبيق خصم", 
            "أدخل مبلغ الخصم:",
            minvalue=0,
            maxvalue=self.subtotal
        )
        
        if discount is not None:
            self.discount = discount
            self.update_totals()
    
    def update_cart_totals(self):
        """Update the cart totals"""
        self.subtotal = sum(item['total'] for item in self.cart_items)
        self.tax_amount = (self.subtotal - self.discount) * self.tax_rate
        self.total = self.subtotal - self.discount + self.tax_amount
        self.update_totals()
    
    def update_totals(self):
        """Update the total displays"""
        self.subtotal_var.set(f"{self.subtotal:.2f}")
        self.discount_var.set(f"{self.discount:.2f}")
        self.tax_var.set(f"{self.tax_amount:.2f}")
        self.total_var.set(f"{self.total:.2f}")
    
    def select_customer(self):
        """Select a customer for the sale"""
        # This would open a customer selection dialog
        # For now, we'll use a simple dialog
        customer_id = simpledialog.askinteger(
            "اختيار العميل", 
            "أدخل رقم العميل:",
            minvalue=1
        )
        
        if customer_id:
            customer = self.customer_model.get_customer_by_id(customer_id)
            if customer:
                self.current_customer_id = customer_id
                self.customer_var.set(customer[1])  # Set customer name
            else:
                messagebox.showwarning("تنبيه", "لم يتم العثور على العميل")
    
    def checkout(self):
        """Complete the sale and process payment"""
        if not self.cart_items:
            messagebox.showinfo("تنبيه", "لا توجد عناصر في السلة")
            return
        
        # Get payment method
        payment_method = self.payment_var.get()
        
        # Confirm checkout
        if messagebox.askyesno("تأكيد الدفع", f"هل أنت متأكد من إتمام عملية الدفع بقيمة {self.total:.2f}؟"):
            # Add items to sale
            for item in self.cart_items:
                self.sale_model.add_sale_item(
                    self.current_sale_id,
                    item['product_id'],
                    item['quantity'],
                    item['price']
                )
            
            # Update sale totals
            self.sale_model.update_sale_totals(
                self.current_sale_id,
                self.discount,
                self.tax_amount
            )
            
            # Finalize sale
            self.sale_model.finalize_sale(
                self.current_sale_id,
                payment_method,
                "paid"
            )
            
            # Show success message
            messagebox.showinfo("نجاح", f"تم إتمام عملية الدفع بنجاح\nرقم الفاتورة: {self.current_invoice}")
            
            # Print receipt (would be implemented)
            self.print_receipt()
            
            # Start a new sale
            self.new_sale()
    
    def print_receipt(self):
        """Print the receipt"""
        # This would implement receipt printing
        # For now, just show a message
        print(f"Printing receipt for invoice {self.current_invoice}")
    
    def hold_sale(self):
        """Hold the current sale for later"""
        if not self.cart_items:
            messagebox.showinfo("تنبيه", "لا توجد عناصر في السلة")
            return
        
        # This would implement sale holding functionality
        messagebox.showinfo("تعليق الفاتورة", "تم تعليق الفاتورة بنجاح")
    
    def recall_sale(self):
        """Recall a held sale"""
        # This would implement recalling held sales
        messagebox.showinfo("استرجاع فاتورة", "ميزة استرجاع الفواتير غير متاحة حالياً")
    
    def void_sale(self):
        """Void the current sale"""
        if not self.cart_items:
            messagebox.showinfo("تنبيه", "لا توجد عناصر في السلة")
            return
        
        if messagebox.askyesno("إلغاء الفاتورة", "هل أنت متأكد من إلغاء الفاتورة الحالية؟"):
            reason = simpledialog.askstring("سبب الإلغاء", "أدخل سبب إلغاء الفاتورة:")
            if reason:
                # Void the sale in the database
                if self.current_sale_id:
                    self.sale_model.void_sale(self.current_sale_id, self.current_user[0], reason)
                
                # Start a new sale
                self.new_sale()
    
    def go_to_dashboard(self):
        """Return to the dashboard"""
        if self.cart_items:
            if messagebox.askyesno("العودة للوحة التحكم", "هناك عناصر في السلة. هل أنت متأكد من العودة للوحة التحكم؟"):
                self.root.destroy()
        else:
            self.root.destroy()


# For testing the POS window independently
if __name__ == "__main__":
    import sqlite3
    from database.db_setup import DatabaseSetup
    
    # Setup database
    db = DatabaseSetup()
    db.connect()
    db.setup_tables()
    db.insert_sample_data()
    
    # Create root window
    root = tk.Tk()
    
    # Mock user for testing
    mock_user = (1, 'admin', 'admin123', 'System Administrator', 'admin', 'day', None, None, None, None, 1)
    
    # Create POS window
    pos_window = POSWindow(root, db.conn, mock_user)
    
    # Start the main loop
    root.mainloop()
    
    # Close database connection
    db.close()