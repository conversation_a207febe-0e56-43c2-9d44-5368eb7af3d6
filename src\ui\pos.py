import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import sqlite3
import os
import sys
import datetime

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from models.product import Product
from models.customer import Customer
from models.sale import Sale
from ui.styles import AppStyles, NeonCard, HolographicButton, ParticleEffect, GlassPanel, CyberProgressBar

class POSWindow:
    def __init__(self, root, db_connection, current_user):
        """Initialize the POS window"""
        self.root = root
        self.conn = db_connection
        self.current_user = current_user
        
        # Initialize models
        self.product_model = Product(self.conn)
        self.customer_model = Customer(self.conn)
        self.sale_model = Sale(self.conn)
        
        # Initialize sale variables
        self.current_sale_id = None
        self.current_invoice = None
        self.current_customer_id = None
        self.cart_items = []
        self.subtotal = 0.0
        self.discount = 0.0
        self.tax_rate = 0.15  # 15% tax rate
        self.tax_amount = 0.0
        self.total = 0.0
        
        # Configure the window
        self.root.title(f"POSMADORA - نقطة البيع - {current_user[3]}")
        self.root.state('zoomed')  # Maximize window
        
        # Set window icon if available
        try:
            self.root.iconbitmap("resources/images/icon.ico")
        except:
            pass
        
        # Configure the root window with cyber theme
        self.root.configure(bg=AppStyles.BG_MAIN)

        # Create main container with particle background
        self.main_container = tk.Frame(self.root, bg=AppStyles.BG_MAIN)
        self.main_container.pack(fill=tk.BOTH, expand=True)

        # Add particle effect background
        self.particle_bg = ParticleEffect(self.main_container, particle_count=20,
                                         bg=AppStyles.BG_MAIN, highlightthickness=0)
        self.particle_bg.place(x=0, y=0, relwidth=1, relheight=1)

        # Create top bar
        self.create_top_bar()

        # Create main content area with left and right panels
        self.content_frame = tk.Frame(self.main_container, bg=AppStyles.BG_MAIN)
        self.content_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # Create left panel (product catalog) with glass effect
        self.left_panel = GlassPanel(self.content_frame)
        self.left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # Create right panel (cart and checkout) with glass effect
        self.right_panel = GlassPanel(self.content_frame)
        self.right_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        self.right_panel.configure(width=450)
        
        # Create product catalog
        self.create_product_catalog()
        
        # Create shopping cart
        self.create_shopping_cart()
        
        # Create checkout panel
        self.create_checkout_panel()
        
        # Start a new sale
        self.new_sale()
    
    def create_top_bar(self):
        """Create the top bar with user info and date/time"""
        self.top_bar = GlassPanel(self.main_container)
        self.top_bar.pack(fill=tk.X, pady=(10, 15))
        self.top_bar.configure(height=80)

        # Create content frame inside glass panel
        top_content = tk.Frame(self.top_bar.canvas, bg=AppStyles.BG_GLASS)
        self.top_bar.canvas.create_window(0, 0, anchor=tk.NW, window=top_content)

        # Left side - Logo/Title with neon effect
        self.title_label = tk.Label(top_content, text="POSMADORA - نقطة البيع",
                                   font=(AppStyles.FONT_FAMILY_ARABIC, 24, 'bold'),
                                   fg=AppStyles.TEXT_NEON, bg=AppStyles.BG_GLASS)
        self.title_label.pack(side=tk.LEFT, padx=20, pady=20)

        # Right side - User info and date/time
        self.info_frame = tk.Frame(top_content, bg=AppStyles.BG_GLASS)
        self.info_frame.pack(side=tk.RIGHT, padx=20, pady=20)

        # Current date and time with cyber styling
        self.datetime_var = tk.StringVar()
        self.update_datetime()
        self.datetime_label = tk.Label(self.info_frame, textvariable=self.datetime_var,
                                      font=(AppStyles.FONT_FAMILY_MONO, 14, 'bold'),
                                      fg=AppStyles.PRIMARY, bg=AppStyles.BG_GLASS)
        self.datetime_label.pack(anchor=tk.E)

        # User info with neon styling
        self.user_label = tk.Label(
            self.info_frame,
            text=f"المستخدم: {self.current_user[3]} | الوردية: {self.current_user[5]}",
            font=(AppStyles.FONT_FAMILY_ARABIC, 12),
            fg=AppStyles.TEXT_LIGHT, bg=AppStyles.BG_GLASS
        )
        self.user_label.pack(anchor=tk.E, pady=(5, 0))
        
        # Schedule datetime update
        self.root.after(1000, self.update_datetime)

        # Center - Quick actions with holographic buttons
        self.actions_frame = tk.Frame(top_content, bg=AppStyles.BG_GLASS)
        self.actions_frame.pack(side=tk.TOP, fill=tk.X, pady=10)

        # New Sale button
        self.new_sale_btn = HolographicButton(
            self.actions_frame, text="🆕 فاتورة جديدة", width=150, height=40,
            command=self.confirm_new_sale, primary_color=AppStyles.PRIMARY
        )
        self.new_sale_btn.pack(side=tk.LEFT, padx=8)

        # Hold Sale button
        self.hold_sale_btn = HolographicButton(
            self.actions_frame, text="⏸️ تعليق الفاتورة", width=150, height=40,
            command=self.hold_sale, primary_color=AppStyles.SECONDARY
        )
        self.hold_sale_btn.pack(side=tk.LEFT, padx=8)

        # Recall Sale button
        self.recall_sale_btn = HolographicButton(
            self.actions_frame, text="🔄 استرجاع فاتورة", width=150, height=40,
            command=self.recall_sale, primary_color=AppStyles.ACCENT
        )
        self.recall_sale_btn.pack(side=tk.LEFT, padx=8)

        # Void Sale button
        self.void_sale_btn = HolographicButton(
            self.actions_frame, text="❌ إلغاء الفاتورة", width=150, height=40,
            command=self.void_sale, primary_color=AppStyles.DANGER
        )
        self.void_sale_btn.pack(side=tk.LEFT, padx=8)
        
        # Customer button
        self.customer_btn = ttk.Button(
            self.actions_frame, text="اختيار العميل", width=15,
            command=self.select_customer
        )
        self.customer_btn.pack(side=tk.LEFT, padx=5)
        
        # Dashboard button
        self.dashboard_btn = ttk.Button(
            self.actions_frame, text="لوحة التحكم", width=15,
            command=self.go_to_dashboard
        )
        self.dashboard_btn.pack(side=tk.LEFT, padx=5)
    
    def update_datetime(self):
        """Update the date and time display"""
        now = datetime.datetime.now()
        self.datetime_var.set(now.strftime("%Y-%m-%d %H:%M:%S"))
        self.root.after(1000, self.update_datetime)
    
    def create_product_catalog(self):
        """Create the product catalog panel"""
        # Create content frame inside glass panel
        self.catalog_content = tk.Frame(self.left_panel.canvas, bg=AppStyles.BG_GLASS)
        self.left_panel.canvas.create_window(0, 0, anchor=tk.NW, window=self.catalog_content)

        # Create frame for search and categories with neon card
        search_card = NeonCard(self.catalog_content, glow_color=AppStyles.PRIMARY_GLOW)
        search_card.pack(fill=tk.X, padx=15, pady=15)

        self.catalog_top_frame = tk.Frame(search_card.card_frame, bg=AppStyles.BG_CARD)
        self.catalog_top_frame.pack(fill=tk.X, padx=10, pady=10)

        # Search bar with cyber styling
        search_frame = tk.Frame(self.catalog_top_frame, bg=AppStyles.BG_CARD)
        search_frame.pack(fill=tk.X, pady=(0, 10))

        tk.Label(search_frame, text="🔍 البحث:", font=(AppStyles.FONT_FAMILY_ARABIC, 12, 'bold'),
                fg=AppStyles.TEXT_NEON, bg=AppStyles.BG_CARD).pack(side=tk.LEFT, padx=(0, 10))

        self.search_var = tk.StringVar()
        self.search_entry = tk.Entry(search_frame, textvariable=self.search_var, width=25,
                                    font=(AppStyles.FONT_FAMILY_ARABIC, 12),
                                    bg=AppStyles.BG_SECONDARY, fg=AppStyles.TEXT_LIGHT,
                                    insertbackground=AppStyles.PRIMARY, relief=tk.FLAT)
        self.search_entry.pack(side=tk.LEFT, padx=5, ipady=5)

        self.search_btn = HolographicButton(
            search_frame, text="بحث", width=80, height=35,
            command=self.search_products, primary_color=AppStyles.PRIMARY
        )
        self.search_btn.pack(side=tk.LEFT, padx=10)

        # Barcode entry with cyber styling
        barcode_frame = tk.Frame(self.catalog_top_frame, bg=AppStyles.BG_CARD)
        barcode_frame.pack(fill=tk.X, pady=(0, 10))

        tk.Label(barcode_frame, text="📱 الباركود:", font=(AppStyles.FONT_FAMILY_ARABIC, 12, 'bold'),
                fg=AppStyles.TEXT_NEON, bg=AppStyles.BG_CARD).pack(side=tk.LEFT, padx=(0, 10))

        self.barcode_var = tk.StringVar()
        self.barcode_entry = tk.Entry(barcode_frame, textvariable=self.barcode_var, width=20,
                                     font=(AppStyles.FONT_FAMILY_MONO, 12),
                                     bg=AppStyles.BG_SECONDARY, fg=AppStyles.PRIMARY,
                                     insertbackground=AppStyles.PRIMARY, relief=tk.FLAT)
        self.barcode_entry.pack(side=tk.LEFT, padx=5, ipady=5)
        self.barcode_entry.bind("<Return>", self.scan_barcode)

        # Category filter with cyber styling
        category_frame = tk.Frame(self.catalog_top_frame, bg=AppStyles.BG_CARD)
        category_frame.pack(fill=tk.X)

        tk.Label(category_frame, text="📂 التصنيف:", font=(AppStyles.FONT_FAMILY_ARABIC, 12, 'bold'),
                fg=AppStyles.TEXT_NEON, bg=AppStyles.BG_CARD).pack(side=tk.LEFT, padx=(0, 10))

        self.category_var = tk.StringVar(value="الكل")
        self.categories = ["الكل"] + [cat[1] for cat in self.product_model.get_all_categories()]

        # Create custom category buttons
        for i, category in enumerate(self.categories[:6]):  # Show first 6 categories
            cat_btn = HolographicButton(
                category_frame, text=category, width=100, height=30,
                command=lambda c=category: self.set_category_filter(c),
                primary_color=AppStyles.ACCENT if category == "الكل" else AppStyles.SECONDARY
            )
            cat_btn.pack(side=tk.LEFT, padx=5)
        
        # Create product grid
        self.create_product_grid()

    def set_category_filter(self, category):
        """Set category filter"""
        self.category_var.set(category)
        self.filter_by_category()

    def create_product_grid(self):
        """Create the product grid display"""
        # Create a neon card for the product grid
        products_card = NeonCard(self.catalog_content, title="المنتجات المتاحة",
                                glow_color=AppStyles.ACCENT_GLOW)
        products_card.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))

        # Create a canvas with scrollbar for the products
        self.canvas = tk.Canvas(products_card.card_frame, bg=AppStyles.BG_CARD, highlightthickness=0)
        self.scrollbar = tk.Scrollbar(products_card.card_frame, orient=tk.VERTICAL, command=self.canvas.yview,
                                     bg=AppStyles.BG_SECONDARY, troughcolor=AppStyles.BG_CARD,
                                     activebackground=AppStyles.PRIMARY)
        self.canvas.configure(yscrollcommand=self.scrollbar.set)

        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create a frame inside the canvas to hold the products
        self.product_grid = tk.Frame(self.canvas, bg=AppStyles.BG_CARD)
        self.canvas.create_window((0, 0), window=self.product_grid, anchor=tk.NW)

        # Load products
        self.load_products()

        # Update the scroll region when the product grid changes size
        self.product_grid.bind("<Configure>", lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all")))
    
    def load_products(self, products=None):
        """Load products into the grid"""
        # Clear existing products
        for widget in self.product_grid.winfo_children():
            widget.destroy()

        # Get products if not provided
        if products is None:
            products = self.product_model.get_all_products()

        # Create product cards in a grid
        row, col = 0, 0
        max_cols = 3  # Number of columns in the grid

        for product in products:
            # Create a neon card for each product
            product_card = NeonCard(self.product_grid, glow_color=AppStyles.SECONDARY_GLOW)
            product_card.grid(row=row, column=col, padx=8, pady=8, sticky=tk.NSEW)

            # Configure grid weights for responsive design
            self.product_grid.grid_columnconfigure(col, weight=1)

            # Product content frame
            content_frame = tk.Frame(product_card.card_frame, bg=AppStyles.BG_CARD)
            content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # Product name with neon styling
            name_label = tk.Label(content_frame, text=product[2],
                                 font=(AppStyles.FONT_FAMILY_ARABIC, 14, 'bold'),
                                 fg=AppStyles.TEXT_NEON, bg=AppStyles.BG_CARD,
                                 wraplength=180, justify=tk.CENTER)
            name_label.pack(pady=(0, 8))

            # Product price with cyber styling
            price_label = tk.Label(content_frame, text=f"💰 {product[6]:.2f} ريال",
                                  font=(AppStyles.FONT_FAMILY_MONO, 12, 'bold'),
                                  fg=AppStyles.PRIMARY, bg=AppStyles.BG_CARD)
            price_label.pack(pady=2)

            # Product stock with status color
            stock_color = AppStyles.ACCENT if product[7] > 10 else AppStyles.DANGER if product[7] < 5 else AppStyles.SECONDARY
            stock_label = tk.Label(content_frame, text=f"📦 المخزون: {product[7]}",
                                  font=(AppStyles.FONT_FAMILY_ARABIC, 11),
                                  fg=stock_color, bg=AppStyles.BG_CARD)
            stock_label.pack(pady=2)

            # Add to cart button with holographic effect
            add_btn = HolographicButton(
                content_frame, text="🛒 إضافة للسلة", width=160, height=35,
                command=lambda p=product: self.add_to_cart(p),
                primary_color=AppStyles.ACCENT
            )
            add_btn.pack(pady=(8, 0))
            
            # Update row and column for next product
            col += 1
            if col >= max_cols:
                col = 0
                row += 1
    
    def search_products(self):
        """Search products by name or barcode"""
        search_term = self.search_var.get().strip()
        if search_term:
            products = self.product_model.search_products(search_term)
            self.load_products(products)
        else:
            self.load_products()
    
    def scan_barcode(self, event=None):
        """Handle barcode scanning"""
        barcode = self.barcode_var.get().strip()
        if barcode:
            product = self.product_model.get_product_by_barcode(barcode)
            if product:
                self.add_to_cart(product)
                self.barcode_var.set("")  # Clear barcode field
            else:
                messagebox.showwarning("باركود غير موجود", "لم يتم العثور على منتج بهذا الباركود")
    
    def filter_by_category(self, event=None):
        """Filter products by category"""
        category = self.category_var.get()
        if category == "الكل":
            self.load_products()
        else:
            products = self.product_model.get_products_by_category(category)
            self.load_products(products)
    
    def create_shopping_cart(self):
        """Create the shopping cart panel"""
        # Create content frame inside glass panel
        cart_content = tk.Frame(self.right_panel.canvas, bg=AppStyles.BG_GLASS)
        self.right_panel.canvas.create_window(0, 0, anchor=tk.NW, window=cart_content)

        # Create a neon card for the cart
        cart_card = NeonCard(cart_content, title="🛒 سلة المشتريات",
                            glow_color=AppStyles.PRIMARY_GLOW)
        cart_card.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Create custom cart display instead of treeview
        self.cart_display_frame = tk.Frame(cart_card.card_frame, bg=AppStyles.BG_CARD)
        self.cart_display_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Cart header
        header_frame = tk.Frame(self.cart_display_frame, bg=AppStyles.BG_SECONDARY, height=40)
        header_frame.pack(fill=tk.X, pady=(0, 5))
        header_frame.pack_propagate(False)

        # Header labels with cyber styling
        tk.Label(header_frame, text="المنتج", font=(AppStyles.FONT_FAMILY_ARABIC, 12, 'bold'),
                fg=AppStyles.TEXT_NEON, bg=AppStyles.BG_SECONDARY).pack(side=tk.LEFT, padx=10, pady=10)
        tk.Label(header_frame, text="الكمية", font=(AppStyles.FONT_FAMILY_ARABIC, 12, 'bold'),
                fg=AppStyles.TEXT_NEON, bg=AppStyles.BG_SECONDARY).pack(side=tk.RIGHT, padx=(0, 80), pady=10)
        tk.Label(header_frame, text="المجموع", font=(AppStyles.FONT_FAMILY_ARABIC, 12, 'bold'),
                fg=AppStyles.TEXT_NEON, bg=AppStyles.BG_SECONDARY).pack(side=tk.RIGHT, padx=10, pady=10)

        # Scrollable cart items frame
        self.cart_canvas = tk.Canvas(self.cart_display_frame, bg=AppStyles.BG_CARD, highlightthickness=0, height=300)
        self.cart_scrollbar = tk.Scrollbar(self.cart_display_frame, orient=tk.VERTICAL, command=self.cart_canvas.yview,
                                          bg=AppStyles.BG_SECONDARY, troughcolor=AppStyles.BG_CARD,
                                          activebackground=AppStyles.PRIMARY)
        self.cart_canvas.configure(yscrollcommand=self.cart_scrollbar.set)

        self.cart_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.cart_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Frame inside canvas for cart items
        self.cart_items_frame = tk.Frame(self.cart_canvas, bg=AppStyles.BG_CARD)
        self.cart_canvas.create_window((0, 0), window=self.cart_items_frame, anchor=tk.NW)

        # Create cart action buttons with holographic effect
        self.cart_actions = tk.Frame(cart_card.card_frame, bg=AppStyles.BG_CARD)
        self.cart_actions.pack(fill=tk.X, padx=10, pady=(0, 10))

        # Remove item button
        self.remove_btn = HolographicButton(
            self.cart_actions, text="🗑️ حذف العنصر", width=140, height=35,
            command=self.remove_from_cart, primary_color=AppStyles.DANGER
        )
        self.remove_btn.pack(side=tk.LEFT, padx=5)

        # Clear cart button
        self.clear_btn = HolographicButton(
            self.cart_actions, text="🧹 تفريغ السلة", width=140, height=35,
            command=self.clear_cart, primary_color=AppStyles.SECONDARY
        )
        self.clear_btn.pack(side=tk.RIGHT, padx=5)
        
        # Apply discount button
        self.discount_btn = HolographicButton(
            self.cart_actions, text="💸 خصم", width=100, height=35,
            command=self.apply_discount, primary_color=AppStyles.ACCENT
        )
        self.discount_btn.pack(side=tk.LEFT, padx=5, expand=True)
    
    def create_checkout_panel(self):
        """Create the checkout panel"""
        # Get cart content from shopping cart creation
        cart_content = self.right_panel.canvas.winfo_children()[0]

        # Create a neon card for checkout
        self.checkout_card = NeonCard(cart_content, title="💳 الدفع والإنهاء",
                                     glow_color=AppStyles.ACCENT_GLOW)
        self.checkout_card.pack(fill=tk.X, padx=15, pady=(0, 15))

        # Customer info with cyber styling
        customer_frame = tk.Frame(self.checkout_card.card_frame, bg=AppStyles.BG_CARD)
        customer_frame.pack(fill=tk.X, padx=10, pady=(10, 5))

        tk.Label(customer_frame, text="👤 العميل:", font=(AppStyles.FONT_FAMILY_ARABIC, 12, 'bold'),
                fg=AppStyles.TEXT_NEON, bg=AppStyles.BG_CARD).pack(side=tk.LEFT, padx=5)
        self.customer_var = tk.StringVar(value="عميل عابر")
        self.customer_label = tk.Label(customer_frame, textvariable=self.customer_var,
                                      font=(AppStyles.FONT_FAMILY_ARABIC, 12, 'bold'),
                                      fg=AppStyles.PRIMARY, bg=AppStyles.BG_CARD)
        self.customer_label.pack(side=tk.LEFT, padx=10)

        # Invoice info with cyber styling
        invoice_frame = tk.Frame(self.checkout_card.card_frame, bg=AppStyles.BG_CARD)
        invoice_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(invoice_frame, text="📄 رقم الفاتورة:", font=(AppStyles.FONT_FAMILY_ARABIC, 12, 'bold'),
                fg=AppStyles.TEXT_NEON, bg=AppStyles.BG_CARD).pack(side=tk.LEFT, padx=5)
        self.invoice_var = tk.StringVar()
        self.invoice_label = tk.Label(invoice_frame, textvariable=self.invoice_var,
                                     font=(AppStyles.FONT_FAMILY_MONO, 12, 'bold'),
                                     fg=AppStyles.SECONDARY, bg=AppStyles.BG_CARD)
        self.invoice_label.pack(side=tk.LEFT, padx=10)

        # Totals with glass panel effect
        totals_panel = GlassPanel(self.checkout_card.card_frame)
        totals_panel.pack(fill=tk.X, padx=10, pady=10)

        self.totals_frame = tk.Frame(totals_panel.canvas, bg=AppStyles.BG_GLASS)
        totals_panel.canvas.create_window(0, 0, anchor=tk.NW, window=self.totals_frame)

        # Subtotal
        self.create_total_row(self.totals_frame, "💰 المجموع الفرعي:", "subtotal_var", 0, AppStyles.TEXT_LIGHT)

        # Discount
        self.create_total_row(self.totals_frame, "💸 الخصم:", "discount_var", 1, AppStyles.SECONDARY)

        # Tax
        self.create_total_row(self.totals_frame, "📊 الضريبة:", "tax_var", 2, AppStyles.ACCENT)

        # Total with special styling
        self.create_total_row(self.totals_frame, "🎯 المجموع الكلي:", "total_var", 3, AppStyles.PRIMARY, True)

    def create_total_row(self, parent, label_text, var_name, row, color, is_total=False):
        """Create a row for totals display"""
        font_size = 14 if is_total else 12
        font_weight = 'bold' if is_total else 'normal'

        # Create the variable if it doesn't exist
        if not hasattr(self, var_name):
            setattr(self, var_name, tk.StringVar(value="0.00"))

        # Label
        tk.Label(parent, text=label_text,
                font=(AppStyles.FONT_FAMILY_ARABIC, font_size, font_weight),
                fg=AppStyles.TEXT_NEON, bg=AppStyles.BG_GLASS).grid(
                row=row, column=0, sticky=tk.W, padx=10, pady=5)

        # Value
        var = getattr(self, var_name)
        tk.Label(parent, textvariable=var,
                font=(AppStyles.FONT_FAMILY_MONO, font_size, font_weight),
                fg=color, bg=AppStyles.BG_GLASS).grid(
                row=row, column=1, sticky=tk.E, padx=10, pady=5)

        # Payment methods with holographic buttons
        payment_frame = tk.Frame(self.checkout_card.card_frame, bg=AppStyles.BG_CARD)
        payment_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(payment_frame, text="💳 طريقة الدفع:", font=(AppStyles.FONT_FAMILY_ARABIC, 12, 'bold'),
                fg=AppStyles.TEXT_NEON, bg=AppStyles.BG_CARD).pack(pady=(0, 10))

        # Payment method buttons
        payment_buttons_frame = tk.Frame(payment_frame, bg=AppStyles.BG_CARD)
        payment_buttons_frame.pack(fill=tk.X)

        self.payment_var = tk.StringVar(value="cash")

        # Cash button
        self.cash_btn = HolographicButton(
            payment_buttons_frame, text="💵 نقدي", width=120, height=40,
            command=lambda: self.set_payment_method("cash"),
            primary_color=AppStyles.ACCENT
        )
        self.cash_btn.pack(side=tk.LEFT, padx=5)

        # Card button
        self.card_btn = HolographicButton(
            payment_buttons_frame, text="💳 بطاقة", width=120, height=40,
            command=lambda: self.set_payment_method("card"),
            primary_color=AppStyles.SECONDARY
        )
        self.card_btn.pack(side=tk.LEFT, padx=5)

        # Bank transfer button
        self.bank_btn = HolographicButton(
            payment_buttons_frame, text="🏦 تحويل", width=120, height=40,
            command=lambda: self.set_payment_method("bank"),
            primary_color=AppStyles.PRIMARY
        )
        self.bank_btn.pack(side=tk.LEFT, padx=5)

        # Checkout button with special effect
        checkout_btn_frame = tk.Frame(checkout_card.card_frame, bg=AppStyles.BG_CARD)
        checkout_btn_frame.pack(fill=tk.X, padx=10, pady=(10, 15))

        self.checkout_btn = HolographicButton(
            checkout_btn_frame, text="🚀 إتمام عملية الدفع", width=400, height=50,
            command=self.checkout, primary_color=AppStyles.ACCENT,
            font_size=16
        )
        self.checkout_btn.pack(fill=tk.X)

    def set_payment_method(self, method):
        """Set the payment method"""
        self.payment_var.set(method)
    
    def new_sale(self):
        """Start a new sale"""
        # Create a new sale in the database
        sale_id, invoice = self.sale_model.create_sale(self.current_user[0])
        
        if sale_id:
            self.current_sale_id = sale_id
            self.current_invoice = invoice
            self.current_customer_id = None
            self.invoice_var.set(invoice)
            self.customer_var.set("عميل عابر")
            
            # Clear the cart
            self.cart_items = []
            self.cart_tree.delete(*self.cart_tree.get_children())
            
            # Reset totals
            self.subtotal = 0.0
            self.discount = 0.0
            self.tax_amount = 0.0
            self.total = 0.0
            self.update_totals()
            
            # Focus on barcode entry
            self.barcode_entry.focus_set()
        else:
            messagebox.showerror("خطأ", "فشل في إنشاء فاتورة جديدة")
    
    def confirm_new_sale(self):
        """Confirm starting a new sale if there are items in the cart"""
        if self.cart_items:
            if messagebox.askyesno("فاتورة جديدة", "هناك عناصر في السلة. هل أنت متأكد من بدء فاتورة جديدة؟"):
                self.new_sale()
        else:
            self.new_sale()
    
    def add_to_cart(self, product):
        """Add a product to the cart"""
        if not self.current_sale_id:
            self.new_sale()
        
        # Check if product is already in cart
        for i, item in enumerate(self.cart_items):
            if item['product_id'] == product[0]:
                # Update quantity
                item['quantity'] += 1
                item['total'] = item['quantity'] * item['price']
                
                # Update treeview
                item_id = self.cart_tree.get_children()[i]
                self.cart_tree.item(item_id, values=(
                    i+1, 
                    item['name'], 
                    f"{item['price']:.2f}", 
                    item['quantity'], 
                    f"{item['total']:.2f}"
                ))
                
                self.update_cart_totals()
                return
        
        # Add new item to cart
        item = {
            'product_id': product[0],
            'name': product[2],
            'price': product[6],
            'quantity': 1,
            'total': product[6]
        }
        self.cart_items.append(item)
        
        # Add to treeview
        self.cart_tree.insert('', tk.END, values=(
            len(self.cart_items), 
            item['name'], 
            f"{item['price']:.2f}", 
            item['quantity'], 
            f"{item['total']:.2f}"
        ))
        
        self.update_cart_totals()
    
    def edit_cart_item(self, event):
        """Edit the quantity of a cart item"""
        # Get selected item
        selection = self.cart_tree.selection()
        if not selection:
            return
        
        item_id = selection[0]
        item_index = self.cart_tree.index(item_id)
        
        # Ask for new quantity
        new_quantity = simpledialog.askinteger(
            "تعديل الكمية", 
            "أدخل الكمية الجديدة:",
            initialvalue=self.cart_items[item_index]['quantity'],
            minvalue=1,
            maxvalue=1000
        )
        
        if new_quantity:
            # Update cart item
            self.cart_items[item_index]['quantity'] = new_quantity
            self.cart_items[item_index]['total'] = new_quantity * self.cart_items[item_index]['price']
            
            # Update treeview
            self.cart_tree.item(item_id, values=(
                item_index+1, 
                self.cart_items[item_index]['name'], 
                f"{self.cart_items[item_index]['price']:.2f}", 
                new_quantity, 
                f"{self.cart_items[item_index]['total']:.2f}"
            ))
            
            self.update_cart_totals()
    
    def remove_from_cart(self):
        """Remove selected item from cart"""
        selection = self.cart_tree.selection()
        if not selection:
            messagebox.showinfo("تنبيه", "الرجاء اختيار عنصر من السلة أولاً")
            return
        
        item_id = selection[0]
        item_index = self.cart_tree.index(item_id)
        
        # Remove from cart items
        self.cart_items.pop(item_index)
        
        # Remove from treeview
        self.cart_tree.delete(item_id)
        
        # Update item numbers
        for i, child in enumerate(self.cart_tree.get_children()):
            values = self.cart_tree.item(child, 'values')
            self.cart_tree.item(child, values=(i+1,) + values[1:])
        
        self.update_cart_totals()
    
    def clear_cart(self):
        """Clear all items from the cart"""
        if not self.cart_items:
            return
            
        if messagebox.askyesno("تفريغ السلة", "هل أنت متأكد من تفريغ السلة؟"):
            self.cart_items = []
            self.cart_tree.delete(*self.cart_tree.get_children())
            self.update_cart_totals()
    
    def apply_discount(self):
        """Apply a discount to the sale"""
        if not self.cart_items:
            messagebox.showinfo("تنبيه", "لا توجد عناصر في السلة")
            return
        
        # Ask for discount amount
        discount = simpledialog.askfloat(
            "تطبيق خصم", 
            "أدخل مبلغ الخصم:",
            minvalue=0,
            maxvalue=self.subtotal
        )
        
        if discount is not None:
            self.discount = discount
            self.update_totals()
    
    def update_cart_totals(self):
        """Update the cart totals"""
        self.subtotal = sum(item['total'] for item in self.cart_items)
        self.tax_amount = (self.subtotal - self.discount) * self.tax_rate
        self.total = self.subtotal - self.discount + self.tax_amount
        self.update_totals()
    
    def update_totals(self):
        """Update the total displays"""
        self.subtotal_var.set(f"{self.subtotal:.2f}")
        self.discount_var.set(f"{self.discount:.2f}")
        self.tax_var.set(f"{self.tax_amount:.2f}")
        self.total_var.set(f"{self.total:.2f}")
    
    def select_customer(self):
        """Select a customer for the sale"""
        # This would open a customer selection dialog
        # For now, we'll use a simple dialog
        customer_id = simpledialog.askinteger(
            "اختيار العميل", 
            "أدخل رقم العميل:",
            minvalue=1
        )
        
        if customer_id:
            customer = self.customer_model.get_customer_by_id(customer_id)
            if customer:
                self.current_customer_id = customer_id
                self.customer_var.set(customer[1])  # Set customer name
            else:
                messagebox.showwarning("تنبيه", "لم يتم العثور على العميل")
    
    def checkout(self):
        """Complete the sale and process payment"""
        if not self.cart_items:
            messagebox.showinfo("تنبيه", "لا توجد عناصر في السلة")
            return
        
        # Get payment method
        payment_method = self.payment_var.get()
        
        # Confirm checkout
        if messagebox.askyesno("تأكيد الدفع", f"هل أنت متأكد من إتمام عملية الدفع بقيمة {self.total:.2f}؟"):
            # Add items to sale
            for item in self.cart_items:
                self.sale_model.add_sale_item(
                    self.current_sale_id,
                    item['product_id'],
                    item['quantity'],
                    item['price']
                )
            
            # Update sale totals
            self.sale_model.update_sale_totals(
                self.current_sale_id,
                self.discount,
                self.tax_amount
            )
            
            # Finalize sale
            self.sale_model.finalize_sale(
                self.current_sale_id,
                payment_method,
                "paid"
            )
            
            # Show success message
            messagebox.showinfo("نجاح", f"تم إتمام عملية الدفع بنجاح\nرقم الفاتورة: {self.current_invoice}")
            
            # Print receipt (would be implemented)
            self.print_receipt()
            
            # Start a new sale
            self.new_sale()
    
    def print_receipt(self):
        """Print the receipt"""
        # This would implement receipt printing
        # For now, just show a message
        print(f"Printing receipt for invoice {self.current_invoice}")
    
    def hold_sale(self):
        """Hold the current sale for later"""
        if not self.cart_items:
            messagebox.showinfo("تنبيه", "لا توجد عناصر في السلة")
            return
        
        # This would implement sale holding functionality
        messagebox.showinfo("تعليق الفاتورة", "تم تعليق الفاتورة بنجاح")
    
    def recall_sale(self):
        """Recall a held sale"""
        # This would implement recalling held sales
        messagebox.showinfo("استرجاع فاتورة", "ميزة استرجاع الفواتير غير متاحة حالياً")
    
    def void_sale(self):
        """Void the current sale"""
        if not self.cart_items:
            messagebox.showinfo("تنبيه", "لا توجد عناصر في السلة")
            return
        
        if messagebox.askyesno("إلغاء الفاتورة", "هل أنت متأكد من إلغاء الفاتورة الحالية؟"):
            reason = simpledialog.askstring("سبب الإلغاء", "أدخل سبب إلغاء الفاتورة:")
            if reason:
                # Void the sale in the database
                if self.current_sale_id:
                    self.sale_model.void_sale(self.current_sale_id, self.current_user[0], reason)
                
                # Start a new sale
                self.new_sale()
    
    def go_to_dashboard(self):
        """Return to the dashboard"""
        if self.cart_items:
            if messagebox.askyesno("العودة للوحة التحكم", "هناك عناصر في السلة. هل أنت متأكد من العودة للوحة التحكم؟"):
                self.root.destroy()
        else:
            self.root.destroy()


# For testing the POS window independently
if __name__ == "__main__":
    import sqlite3
    from database.db_setup import DatabaseSetup
    
    # Setup database
    db = DatabaseSetup()
    db.connect()
    db.setup_tables()
    db.insert_sample_data()
    
    # Create root window
    root = tk.Tk()
    
    # Mock user for testing
    mock_user = (1, 'admin', 'admin123', 'System Administrator', 'admin', 'day', None, None, None, None, 1)
    
    # Create POS window
    pos_window = POSWindow(root, db.conn, mock_user)
    
    # Start the main loop
    root.mainloop()
    
    # Close database connection
    db.close()